import React from 'react';
import { View, Text, TouchableOpacity, SafeAreaView } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { RootStackParamList } from '../../navigator/StackNavigator';
import { useTheme } from '../../context/ThemeContext';
import Icon from 'react-native-vector-icons/FontAwesome';
// ZYNC 


type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const SuiteScreen: React.FC = () => {
    const navigation = useNavigation<NavigationProp>();
    const { theme } = useTheme();

    return (
        <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
            <View className="flex-1 justify-center items-center px-6">
                {/* Row 1: Title */}
                <View className="items-center mb-8">
                    <Text className={`text-2xl font-bold uppercase ${theme.colors.text}`}>SUITE</Text>
                </View>

                {/* Row 2: Big Z Logo */}
                <View className="items-center mb-8 mt-10">
                    <View className={`w-28 h-28 rounded-full justify-center items-center ${theme.colors.surface}`}>
                        <Text className="text-blue-400 text-5xl font-bold">Z</Text>
                    </View>
                </View>

                {/* Row 3: CHANNELS (=) | ZYNC text | REELS (=) */}
                <View className="flex-row justify-around items-center mb-8 w-full">
                    {/* CHANNELS with icon */}
                    <TouchableOpacity className="items-center" onPress={() => navigation.navigate('zync-chat')}>
                        <View className={`w-14 h-14 rounded-full justify-center items-center ${theme.colors.surface}`}>
                            <Icon name="bars" size={18} color={theme.isDark ? "white" : "#374151"} />
                        </View>
                        <Text className={`text-xs mt-1 ${theme.colors.text}`}>CHATS</Text>
                    </TouchableOpacity>

                    {/* ZYNC text only */}
                    <View className="items-center">
                        <Text className="text-blue-500 text-5xl font-bold">ZYNC</Text>
                    </View>

                    {/* REELS with icon */}
                    <TouchableOpacity className="items-center" onPress={() => navigation.navigate('zync-reel')}>
                    <View className={`w-14 h-14 rounded-full justify-center items-center ${theme.colors.surface}`}>
                            <Icon name="user" size={18} color={theme.isDark ? "white" : "#374151"} />
                        </View>
                        <Text className={`text-xs mt-1 ${theme.colors.text}`}>REELS</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 4: NEWS (icon) | ZYNC (= icon) */}
                <View className="flex-row justify-evenly items-center mb-12 w-full">
                    {/* NEWS with icon */}
                    <TouchableOpacity className="items-center" onPress={() => navigation.navigate('zync-news')}>
                        <View className={`w-14 h-14 rounded-full justify-center items-center ${theme.colors.surface}`}>
                            <Icon name="file-text-o" size={18} color={theme.isDark ? "white" : "#374151"} />
                        </View>
                        <Text className={`text-xs mt-1 ${theme.colors.text}`}>NEWS</Text>
                    </TouchableOpacity>

                    {/* ZYNC with icon */}
                    <TouchableOpacity className="items-center" onPress={() => navigation.navigate('zync-channels')}>
                    <View className={`w-14 h-14 rounded-full justify-center items-center ${theme.colors.surface}`}>
                            <Icon name="users" size={18} color={theme.isDark ? "white" : "#374151"} />
                        </View>
                    
                        <Text className={`text-xs mt-1 ${theme.colors.text}`}>CHANNELS</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 5: Back Link */}
                <View className="w-full mt-6">
                    <TouchableOpacity
                        onPress={() => navigation.goBack()}
                        className="flex-row items-center justify-center"
                    >
                        <Icon name="arrow-left" size={16} color={theme.isDark ? "white" : "#374151"}  style={{ marginRight: 4 }} />
                        <Text className={`font-medium text-sm ${theme.colors.text}`}>Back</Text>
                    </TouchableOpacity>
                </View>

                {/* Row 6: Explore Button */}
                <View className="w-full mt-4">
                    <TouchableOpacity
                        className="bg-blue-500 rounded-xl py-4 w-full"
                        onPress={() => navigation.navigate('auth/login')}
                    >
                        <Text className="text-white text-center font-bold text-lg">Explore</Text>
                    </TouchableOpacity>
                </View>
            </View>
        </SafeAreaView>
    );
};

export default SuiteScreen;
