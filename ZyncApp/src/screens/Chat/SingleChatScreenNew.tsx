import React, { useEffect, useState } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import Loader from '../../components/common/Loader';
import Icon from 'react-native-vector-icons/Ionicons';

// Import Stream Chat components
import {
  Chat,
  Channel,
  MessageList,
  MessageInput,
  OverlayProvider,
  useCreateChatClient,
} from 'stream-chat-react-native';
import { StreamChat } from 'stream-chat';
import userServices from '../../services/userServices';

type SingleChatScreenRouteProp = RouteProp<RootStackParamList, 'chat/conversation'>;

const SingleChatScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<SingleChatScreenRouteProp>();
  const { chatId, chatName } = route.params;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [channel, setChannel] = useState<any>(null);

  // Get current user data
  const currentUser = userServices.getCurrentUser();
  
  // Create Stream Chat client
  const chatClient = useCreateChatClient({
    apiKey: 'your-api-key-here', // Replace with your actual API key
    userData: {
      id: currentUser?.id || 'user-1',
      name: currentUser?.name || 'User',
    },
    tokenOrProvider: 'your-user-token-here', // Replace with your actual token
  });

  useEffect(() => {
    const initializeChannel = async () => {
      if (!chatClient) {
        console.log('Chat client not ready yet');
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Create or get the channel
        const channelInstance = chatClient.channel('messaging', chatId, {
          name: chatName,
          members: [currentUser?.id || 'user-1'], // Add current user as member
        });

        // Watch the channel to get real-time updates
        await channelInstance.watch();
        
        setChannel(channelInstance);
        console.log('✅ Channel initialized successfully');
      } catch (err) {
        console.error('❌ Failed to initialize channel:', err);
        setError('Failed to load chat');
      } finally {
        setIsLoading(false);
      }
    };

    initializeChannel();
  }, [chatClient, chatId, chatName, currentUser?.id]);

  // Show loading while chat client is being created
  if (!chatClient) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center">
          <Loader />
          <Text className={`mt-4 ${theme.colors.textSecondary}`}>
            Connecting to chat...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show loading while channel is being initialized
  if (isLoading) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        {/* Custom Header */}
        <View className={`flex-row items-center px-4 py-3 border-b ${theme.colors.border}`}>
          <TouchableOpacity 
            onPress={() => navigation.goBack()}
            className="mr-3"
          >
            <Icon name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text className={`text-lg font-semibold ${theme.colors.text} flex-1`}>
            {chatName}
          </Text>
        </View>

        <View className="flex-1 justify-center items-center">
          <Loader />
          <Text className={`mt-4 ${theme.colors.textSecondary}`}>
            Loading chat...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Show error state
  if (error) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        {/* Custom Header */}
        <View className={`flex-row items-center px-4 py-3 border-b ${theme.colors.border}`}>
          <TouchableOpacity 
            onPress={() => navigation.goBack()}
            className="mr-3"
          >
            <Icon name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text className={`text-lg font-semibold ${theme.colors.text} flex-1`}>
            {chatName}
          </Text>
        </View>

        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary} mb-4`}>
            {error}
          </Text>
          <TouchableOpacity 
            onPress={() => {
              setError(null);
              setIsLoading(true);
            }}
            className={`px-6 py-3 rounded-lg ${theme.colors.primary}`}
          >
            <Text className="text-white font-medium">Try Again</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <OverlayProvider>
        <Chat client={chatClient}>
          {/* Custom Header */}
          <View className={`flex-row items-center px-4 py-3 border-b ${theme.colors.border}`}>
            <TouchableOpacity 
              onPress={() => navigation.goBack()}
              className="mr-3"
            >
              <Icon name="arrow-back" size={24} color={theme.colors.text} />
            </TouchableOpacity>
            <Text className={`text-lg font-semibold ${theme.colors.text} flex-1`}>
              {chatName}
            </Text>
          </View>

          {/* Chat Interface */}
          <Channel channel={channel}>
            <View className="flex-1">
              <MessageList />
              <MessageInput />
            </View>
          </Channel>
        </Chat>
      </OverlayProvider>
    </SafeAreaView>
  );
};

export default SingleChatScreen;
