import React, { useEffect, useState } from 'react';
import { View, Text, SafeAreaView, Alert } from 'react-native';
import { useRoute, RouteProp } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import chatServices from '../../services/chatServices';
import Loader from '../../components/common/Loader';

// Import Stream Chat components with error handling
let Chat: React.ComponentType<any> | undefined;
let Channel: React.ComponentType<any> | undefined;
let MessageList: React.ComponentType<any> | undefined;
let MessageInput: React.ComponentType<any> | undefined;
let Thread: React.ComponentType<any> | undefined;

interface StreamChatComponents {
  Chat?: React.ComponentType<any>;
  Channel?: React.ComponentType<any>;
  MessageList?: React.ComponentType<any>;
  MessageInput?: React.ComponentType<any>;
  Thread?: React.ComponentType<any>;
}
try {
  const streamComponents = require('stream-chat-react-native');
  Chat = streamComponents.Chat;
  Channel = streamComponents.Channel;
  MessageList = streamComponents.MessageList;
  MessageInput = streamComponents.MessageInput;
  Thread = streamComponents.Thread;
} catch (error) {
  console.warn('Stream Chat components not available:', error);
}

type SingleChatScreenRouteProp = RouteProp<RootStackParamList, 'chat/conversation'>;

const SingleChatScreen: React.FC = () => {
  const { theme } = useTheme();
  const route = useRoute<SingleChatScreenRouteProp>();
  const { chatId, chatName } = route.params;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [channel, setChannel] = useState<any>(null);

  useEffect(() => {
    const initializeChannel = async () => {
      try {
        const client = chatServices.getClient();
        if (!client) {
          setError('Chat not initialized');
          return;
        }

        // Get the channel by ID
        const channelInstance = client.channel('messaging', chatId);
        await channelInstance.watch();

        setChannel(channelInstance);
      } catch (error: any) {
        console.error('Error initializing channel:', error);
        setError('Failed to load chat');
      } finally {
        setIsLoading(false);
      }
    };

    initializeChannel();
  }, [chatId]);

  if (isLoading) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <Loader text="Loading chat..." />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-xl font-bold ${theme.colors.text} mb-4`}>
            Error
          </Text>
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            {error}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!channel) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            No chat available
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const client = chatServices.getClient();
  if (!client) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            Chat client not available
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Check if Stream Chat components are available
  if (!Chat || !Channel || !MessageList || !MessageInput) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            Chat components not available
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <Chat client={client}>
        <Channel channel={channel}>
          <View className="flex-1">
            <MessageList />
            <MessageInput />
          </View>
          {Thread && <Thread />}
        </Channel>
      </Chat>
    </SafeAreaView>
  );
};

export default SingleChatScreen;
