import React, { useEffect, useState } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import chatServices from '../../services/chatServices';
import Loader from '../../components/common/Loader';
import Icon from 'react-native-vector-icons/Ionicons';

// Import Stream Chat components
import {
  Chat,
  Channel,
  MessageList,
  MessageInput,
} from 'stream-chat-react-native';

type SingleChatScreenRouteProp = RouteProp<RootStackParamList, 'chat/conversation'>;

const SingleChatScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<SingleChatScreenRouteProp>();
  const { chatId, chatName } = route.params;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [channel, setChannel] = useState<any>(null);

  useEffect(() => {
    const initializeChannel = async () => {
      try {
        const client = chatServices.getClient();
        if (!client) {
          setError('Chat not initialized');
          return;
        }

        // Get the channel by ID
        const channelInstance = client.channel('messaging', chatId);
        await channelInstance.watch();

        setChannel(channelInstance);
      } catch (error: any) {
        console.error('Error initializing channel:', error);
        setError('Failed to load chat');
      } finally {
        setIsLoading(false);
      }
    };

    initializeChannel();
  }, [chatId]);

  if (isLoading) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <Loader text="Loading chat..." />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-xl font-bold ${theme.colors.text} mb-4`}>
            Error
          </Text>
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            {error}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!channel) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            No chat available
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const client = chatServices.getClient();
  if (!client) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            Chat client not available
          </Text>
        </View>
      </SafeAreaView>
    );
  }



  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Custom Header */}
      <View className={`flex-row items-center px-4 py-3 border-b ${theme.colors.border}`}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="mr-3"
        >
          <Icon name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text className={`text-lg font-semibold ${theme.colors.text} flex-1`}>
          {chatName}
        </Text>
      </View>

      {/* Chat Interface */}
      <Chat client={client}>
        <Channel channel={channel}>
          <View className="flex-1">
            <MessageList />
            <MessageInput />
          </View>
        </Channel>
      </Chat>
    </SafeAreaView>
  );
};

export default SingleChatScreen;
