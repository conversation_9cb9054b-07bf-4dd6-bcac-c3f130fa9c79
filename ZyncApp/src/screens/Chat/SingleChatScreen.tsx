import React, { useEffect, useState } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { RootStackParamList } from '../../navigator/StackNavigator';
import chatServices from '../../services/chatServices';
import Loader from '../../components/common/Loader';
import Icon from 'react-native-vector-icons/Ionicons';

// Import Stream Chat components with error handling
let Chat: React.ComponentType<any> | null = null;
let Channel: React.ComponentType<any> | null = null;
let MessageList: React.ComponentType<any> | null = null;
let MessageInput: React.ComponentType<any> | null = null;
let OverlayProvider: React.ComponentType<any> | null = null;

try {
  const streamComponents = require('stream-chat-react-native');
  Chat = streamComponents.Chat;
  Channel = streamComponents.Channel;
  MessageList = streamComponents.MessageList;
  MessageInput = streamComponents.MessageInput;
  OverlayProvider = streamComponents.OverlayProvider;
  console.log('✅ Stream Chat components loaded successfully');
} catch (error) {
  console.error('❌ Failed to load Stream Chat components:', error);
}

type SingleChatScreenRouteProp = RouteProp<RootStackParamList, 'chat/conversation'>;

const SingleChatScreen: React.FC = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const route = useRoute<SingleChatScreenRouteProp>();
  const { chatId, chatName } = route.params;

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [channel, setChannel] = useState<any>(null);

  useEffect(() => {
    const initializeChannel = async () => {
      try {
        const client = chatServices.getClient();
        if (!client) {
          setError('Chat not initialized');
          return;
        }

        // Get the channel by ID
        const channelInstance = client.channel('messaging', chatId);
        await channelInstance.watch();

        setChannel(channelInstance);
      } catch (error: any) {
        console.error('Error initializing channel:', error);
        setError('Failed to load chat');
      } finally {
        setIsLoading(false);
      }
    };

    initializeChannel();
  }, [chatId]);

  if (isLoading) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <Loader text="Loading chat..." />
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-xl font-bold ${theme.colors.text} mb-4`}>
            Error
          </Text>
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            {error}
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (!channel) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            No chat available
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const client = chatServices.getClient();
  if (!client) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary}`}>
            Chat client not available
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  // Check if Stream Chat components are available
  if (!Chat || !Channel || !MessageList || !MessageInput) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        {/* Custom Header */}
        <View className={`flex-row items-center px-4 py-3 border-b ${theme.colors.border}`}>
          <TouchableOpacity
            onPress={() => navigation.goBack()}
            className="mr-3"
          >
            <Icon name="arrow-back" size={24} color={theme.colors.text} />
          </TouchableOpacity>
          <Text className={`text-lg font-semibold ${theme.colors.text} flex-1`}>
            {chatName}
          </Text>
        </View>

        <View className="flex-1 justify-center items-center px-6">
          <Text className={`text-center ${theme.colors.textSecondary} mb-4`}>
            Stream Chat components are not available
          </Text>
          <Text className={`text-center ${theme.colors.textSecondary} text-sm`}>
            Please ensure all dependencies are properly installed
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  const chatTheme = {
    colors: {
      white: theme.isDark ? '#1F2937' : '#FFFFFF',
      black: theme.isDark ? '#FFFFFF' : '#000000',
      grey: theme.isDark ? '#374151' : '#F3F4F6',
      blue_alice: theme.isDark ? '#1F2937' : '#F0F9FF',
      accent_blue: '#3B82F6',
      accent_green: '#10B981',
      accent_red: '#EF4444',
      bg_gradient_start: theme.isDark ? '#1F2937' : '#FFFFFF',
      bg_gradient_end: theme.isDark ? '#111827' : '#F9FAFB',
      border: theme.isDark ? '#374151' : '#E5E7EB',
      grey_gainsboro: theme.isDark ? '#4B5563' : '#DCDCDC',
      grey_whisper: theme.isDark ? '#374151' : '#F5F5F5',
      modal: theme.isDark ? '#1F2937' : '#FFFFFF',
      overlay: 'rgba(0, 0, 0, 0.6)',
      shadow_icon: theme.isDark ? '#000000' : '#00000029',
      targetedMessageBackground: theme.isDark ? '#374151' : '#FEF3C7',
      transparent: 'transparent',
    },
  };

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {/* Custom Header */}
      <View className={`flex-row items-center px-4 py-3 border-b ${theme.colors.border}`}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          className="mr-3"
        >
          <Icon name="arrow-back" size={24} color={theme.colors.text} />
        </TouchableOpacity>
        <Text className={`text-lg font-semibold ${theme.colors.text} flex-1`}>
          {chatName}
        </Text>
      </View>

      {/* Chat Interface */}
      {OverlayProvider ? (
        <OverlayProvider>
          <Chat client={client} style={chatTheme}>
            <Channel channel={channel}>
              <View style={{ flex: 1 }}>
                <MessageList />
                <MessageInput />
              </View>
            </Channel>
          </Chat>
        </OverlayProvider>
      ) : (
        <Chat client={client} style={chatTheme}>
          <Channel channel={channel}>
            <View style={{ flex: 1 }}>
              <MessageList />
              <MessageInput />
            </View>
          </Channel>
        </Chat>
      )}
    </SafeAreaView>
  );
};

export default SingleChatScreen;
