import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, ScrollView, TouchableOpacity, StatusBar, Dimensions, Platform, Image, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../context/ThemeContext';
import SearchBar from '../../components/common/SearchBar';
import Loader from '../../components/common/Loader';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { RootStackParamList } from '../../navigator/StackNavigator';
import userServices, { User } from '../../services/userServices';
import chatServices from '../../services/chatServices';
import { useAuth } from '../../context/AuthContext';

type NavigationProp = NativeStackNavigationProp<RootStackParamList>;

const AllChatsScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useAuth();
  const navigation = useNavigation<NavigationProp>();
  const [searchQuery, setSearchQuery] = useState('');
  const [headerHeight, setHeaderHeight] = useState(0);
  const [screenHeight, setScreenHeight] = useState(0);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [isStartingChat, setIsStartingChat] = useState(false);

  useEffect(() => {
    const calculateHeaderHeight = () => {
      const { height } = Dimensions.get('window');
      const statusBarHeight = Platform.OS === 'ios' ? 0 : StatusBar.currentHeight || 24; // iOS SafeAreaView handles status bar
      const headerContentHeight = 60; // Consistent height for both platforms
      const totalHeaderHeight = statusBarHeight + headerContentHeight;
      
      setScreenHeight(height);
      setHeaderHeight(totalHeaderHeight);
    };

    calculateHeaderHeight();
    
    // Listen for orientation changes
    const subscription = Dimensions.addEventListener('change', calculateHeaderHeight);
    
    return () => subscription?.remove();
  }, []);

  const handleSearch = async (query: string) => {
    console.log('🔍 handleSearch called with query:', query);
    console.log('🔍 Current user:', user);
    setSearchQuery(query);
    setSearchError(null);

    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    if (query.trim().length < 2) {
      return;
    }

    setIsSearching(true);
    try {
      const response = await userServices.searchUsers(query);
      console.log('🔍 Full search response:', JSON.stringify(response, null, 2));
      console.log('🔍 Response success:', response.success);
      console.log('🔍 Response data:', response.data);
      console.log('🔍 Response error:', response.error);

      if (response.success && response.data) {
        console.log('👥 Users found:', response.data);
        console.log('👥 Users count:', response.data.length);
        setSearchResults(response.data);
        console.log('🎯 Search results state updated:', response.data);
      } else {
        console.log('❌ Search failed:', response.error);
        setSearchError(response.error || 'Failed to search users');
        setSearchResults([]);
      }
    } catch (error) {
      console.error('Search error:', error);
      setSearchError('Failed to search users');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  const handleUserSelect = async (selectedUser: User) => {
    if (!user) {
      Alert.alert('Error', 'User not authenticated');
      return;
    }

    setIsStartingChat(true);
    try {
      // Initialize chat if not already done
      if (!chatServices.isInitialized()) {
        const initResult = await chatServices.initializeChat({
          id: user.id.toString(),
          name: user.name || user.username,
          image: (user as any).pic || undefined,
        });

        if (!initResult.success) {
          Alert.alert('Error', initResult.error || 'Failed to initialize chat');
          return;
        }
      }

      // Create or get channel with the selected user
      const channelResult = await chatServices.createDirectChannel(
        selectedUser.id.toString(),
        selectedUser.name || selectedUser.username,
        (selectedUser as any).pic || undefined
      );

      if (!channelResult.success) {
        Alert.alert('Error', channelResult.error || 'Failed to create chat channel');
        return;
      }

      // Navigate to chat screen with channel information
      navigation.navigate('chat/conversation', {
        chatId: channelResult.channel?.id || `user_${selectedUser.id}`,
        chatName: selectedUser.name || selectedUser.username,
      });
    } catch (error) {
      console.error('Error starting chat:', error);
      Alert.alert('Error', 'Failed to start chat');
    } finally {
      setIsStartingChat(false);
    }
  };

  const handleCreateChat = () => {
    // TODO: Navigate to create chat screen
    console.log('Create chat pressed');
  };

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <StatusBar 
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.isDark ? '#000000' : '#FFFFFF'}
      />
      {/* Dynamic Header */}
      <View 
        className={`flex-row justify-between items-center px-4 ${theme.colors.background}`}
        style={{ 
          height: Platform.OS === 'ios' ? 50 : 'auto',
          paddingTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight || 24,
          paddingBottom: Platform.OS === 'ios' ? 0 : 12,
        }}
      >
        <Text className={`text-2xl font-bold ${theme.colors.text}`}>
          Chats
        </Text>
        <TouchableOpacity
          className={`px-4 py-2 rounded-lg ${theme.colors.primary} flex-row items-center justify-center`}
          style={{ display: 'none' }}
          onPress={handleCreateChat}
        >
          <Ionicons name="add" size={20} color="white" />
          <Text className="text-white font-medium text-sm">Create</Text>
        </TouchableOpacity> 
      </View>

      <ScrollView className="flex-1 px-2">
        <SearchBar
          placeholder="Search by name, username, or email..."
          onSearch={handleSearch}
          value={searchQuery}
        />

        {isSearching && (
          <View className="py-8">
            <Loader size="small" text="Searching users..." />
          </View>
        )}

        {searchError && (
          <View className="py-4">
            <Text className={`text-center ${theme.colors.textSecondary} text-sm`}>
              {searchError}
            </Text>
          </View>
        )}

        {!isSearching && searchQuery.length > 0 && searchResults.length === 0 && !searchError && (
          <View className="py-8">
            <Text className={`text-center ${theme.colors.textSecondary} text-lg`}>
              No users found for "{searchQuery}"
            </Text>
          </View>
        )}

        {(() => {
          console.log('🔍 Current searchResults in render:', searchResults);
          console.log('🔍 searchResults.length:', searchResults.length);
          return null;
        })()}

        {searchResults.length > 0 && (
          <View className="py-2">
            <Text className={`text-sm ${theme.colors.textSecondary} mb-4 px-2`}>
              {searchResults.length} user{searchResults.length !== 1 ? 's' : ''} found
            </Text>

            {searchResults.map((searchUser) => (
              <TouchableOpacity
                key={searchUser.id}
                className={`flex-row items-center p-4 mb-2 rounded-lg ${theme.colors.surface} border ${theme.colors.border} ${isStartingChat ? 'opacity-50' : ''}`}
                onPress={() => handleUserSelect(searchUser)}
                activeOpacity={0.7}
                disabled={isStartingChat}
              >
                <View className={`w-12 h-12 rounded-full mr-3 ${theme.colors.primary} items-center justify-center`}>
                  {searchUser.pic ? (
                    <Image
                      source={{ uri: searchUser.pic }}
                      className="w-12 h-12 rounded-full"
                      resizeMode="cover"
                    />
                  ) : (
                    <Text className={`text-white font-bold text-lg`}>
                      {searchUser.name?.charAt(0)?.toUpperCase() || searchUser.username?.charAt(0)?.toUpperCase() || '?'}
                    </Text>
                  )}
                </View>
                
                <View className="flex-1">
                  <Text className={`font-semibold ${theme.colors.text} text-base`}>
                    {searchUser.name || searchUser.username}
                  </Text>
                  <Text className={`${theme.colors.textSecondary} text-sm`}>
                    @{searchUser.username}
                  </Text>
                  {searchUser.email && (
                    <Text className={`${theme.colors.textSecondary} text-xs mt-1`}>
                      {searchUser.email}
                    </Text>
                  )}
                </View>
                
                <Ionicons 
                  name="chatbubble-outline" 
                  size={20} 
                  color={theme.colors.textSecondary} 
                />
              </TouchableOpacity>
            ))}
          </View>
        )}

        {searchQuery.length === 0 && (
          <View className="flex-1 justify-center items-center py-8">
            <Ionicons
              name="search"
              size={48}
              color={theme.isDark ? '#4B5563' : '#9CA3AF'}
              style={{ marginBottom: 16 }}
            />
            <Text className={`text-center ${theme.colors.textSecondary} text-lg mb-2`}>
              Find users to chat with
            </Text>
            <Text className={`text-center ${theme.colors.textSecondary} text-sm px-8`}>
              Search by name, username, or email to start a conversation
            </Text>
          </View>
        )}
      </ScrollView>

      {isStartingChat && (
        <Loader
          overlay={true}
          text="Starting chat..."
        />
      )}
    </SafeAreaView>
  );
};

export default AllChatsScreen;
