import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import { validateOtp } from '../../utils/validators';
import authServices from '../../services/authServices';
// ZYNC 


// Constants
const { height } = Dimensions.get('window');

// Main OTP Verification Component
const OtpVerificationScreen: React.FC = () => {
  // Form data
  const [otp, setOtp] = useState('');
  
  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [isResending, setIsResending] = useState(false);
  
  // Error states
  const [otpError, setOtpError] = useState('');
  const [verificationError, setVerificationError] = useState('');
  
  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { checkAuthStatus } = useAuth();

  // Get params from route (passed from RegisterScreen or LoginScreen)
  const email = (route.params as any)?.email || 'your email';
  const fromScreen = (route.params as any)?.fromScreen || 'register';
  const message = (route.params as any)?.message || 'Please verify your email with OTP.';
  // Remove passedOtp - users should enter OTP manually from email

  // Remove auto-fill functionality - users must enter OTP manually

  // Event Handlers
  // Handles OTP input changes and clears errors
  const handleOtpChange = (text: string) => {
    // Remove any whitespace and convert to uppercase for consistency
    const cleanedText = text.replace(/\s/g, '').toUpperCase();
    setOtp(cleanedText);
    
    // Clear any error or success messages when user starts typing
    if (otpError) {
      setOtpError('');
    }
    if (verificationError) {
      setVerificationError('');
    }
  };

  // Handles the OTP verification with validation
  const handleVerifyOtp = async () => {
    // Clear previous errors
    setOtpError('');
    setVerificationError('');

    // Validate OTP
    if (!otp) {
      setOtpError('Please enter the OTP code');
      return;
    }

    if (!otp || otp.length !== 5) {
      setOtpError('Please enter a valid 5-character OTP code');
      return;
    }

    // Attempt verification
    setIsLoading(true);
    try {
      // Add 1.5 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log('🔍 [OtpVerificationScreen] Sending OTP verification request:', { email, otp });
      const response = await authServices.verifyRegisterOtp({ email, otp });
      console.log('📋 [OtpVerificationScreen] OTP verification response:', response);

      if (response.success && response.data) {
        // Verification successful - user is now logged in automatically
        console.log('✅ [OtpVerificationScreen] OTP verification successful, user logged in');
        
        // Auth data is already stored by authServices.verifyRegisterOtp()
        // Token is already set in API controller
        // User is now authenticated and will be redirected to main app

        // Update auth context
        await checkAuthStatus();

        // Show success message
        setVerificationError('Email verified successfully! Redirecting to chat...');
        
        // Redirect to main app after successful verification
        setTimeout(() => {
          (navigation as any).navigate('app/main');
        }, 2000); // Show success message for 2 seconds then navigate
        
      } else {
        // Handle specific error messages from backend
        setVerificationError(response.error || 'Invalid OTP. Please try again.');
        setIsLoading(false); // Stop loading on error
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
      setIsLoading(false); // Stop loading on network error
    }
  };

  // Handles resending OTP
  const handleResendOtp = async () => {
    setIsResending(true);
    setVerificationError('');
    try {
      // Add 1 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000));

      const response = await authServices.regenerateRegisterOTP(email);

      if (response.success && response.data) {
        setVerificationError('OTP resent to your email successfully.');
      } else {
        setVerificationError(response.error || 'Failed to resend OTP. Please try again.');
      }
    } catch (error: any) {
      console.error('Resend OTP error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
    } finally {
      setIsResending(false);
    }
  };

  // Render Methods
  // Renders the OTP input field with validation error display
  const renderOtpField = () => (
    <View className="mb-6">
              <TextInput
          className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-3 ${theme.colors.text} text-center text-xl ${otpError ? 'border-red-500' : ''}`}
          placeholder="Enter 5-character OTP"
          placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
          value={otp}
          onChangeText={handleOtpChange}
          keyboardType="default"
          autoCapitalize="characters"
          maxLength={5}
          style={{ textAlignVertical: 'center', letterSpacing: 0 }}
        />
      {otpError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1 text-center">{otpError}</Text>
      ) : null}
    </View>
  );

  // Renders the verify button with loading state
  const renderVerifyButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleVerifyOtp}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Verifying...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Verify Account
        </Text>
      )}
    </TouchableOpacity>
  );

  // Renders the resend OTP section
  const renderResendSection = () => (
    <View className="flex-row justify-center mb-4">
      <Text className={`${theme.colors.textSecondary} mr-2`}>
        Didn't receive the code?
      </Text>
      <TouchableOpacity onPress={handleResendOtp} disabled={isResending}>
        <Text className={`font-medium ${isResending ? 'text-gray-400' : 'text-blue-500'}`}>
          {isResending ? 'Sending...' : 'Resend'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView 
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {/* Background Image Section */}
          <View style={{ height: height * 0.26 }}>
            <ImageBackground
              source={require('../../assets/images/auth/auth_bg.png')}
              style={{ flex: 1 }}
              resizeMode="cover"
            />
          </View>

          {/* OTP Verification Form Container */}
          <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
            {/* Welcome Header */}
            <View className="mb-4">
              <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
                Verify Account
              </Text>
              <Text className={`text-start ${theme.colors.textSecondary}`}>
                Enter the 5-character code sent to {email}
              </Text>
            </View>

            {/* Form Fields */}
            {renderOtpField()}

            {/* Verification Message Display */}
            {(verificationError) ? (
              <View className="mb-4">
                <Text className={`text-sm text-center ${
                  verificationError.includes('successfully') || 
                  verificationError.includes('resent')
                    ? 'text-green-600'
                    : 'text-red-600'
                }`}>
                  {verificationError}
                </Text>
              </View>
            ) : null}

            {/* Verify Button */}
            {renderVerifyButton()}

            {/* Resend OTP Section */}
            {renderResendSection()}

            {/* Back to Register Link */}
            <View className="flex-row justify-center">
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Text className="text-blue-500 font-medium">
                  Back to Register
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default OtpVerificationScreen;
