import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, SafeAreaView, ImageBackground, Dimensions, ActivityIndicator, ScrollView, KeyboardAvoidingView, Platform, Alert } from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import Ionicons from 'react-native-vector-icons/Ionicons';
import authServices from '../../services/authServices';
// ZYNC 

// Constants
const { height } = Dimensions.get('window');

// TFA Types
type TFAType = 'totp' | 'email';

// Main TFA Verification Component
const TFAVerificationScreen: React.FC = () => {
  // Form data
  const [tfaType, setTfaType] = useState<TFAType>('totp');
  const [otp, setOtp] = useState('');

  // UI states
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingEmail, setIsSendingEmail] = useState(false);

  // Error states
  const [otpError, setOtpError] = useState('');
  const [verificationError, setVerificationError] = useState('');

  // Navigation and context
  const navigation = useNavigation();
  const route = useRoute();
  const { theme } = useTheme();
  const { checkAuthStatus } = useAuth();

  // Get params from route (passed from LoginScreen)
  const username = (route.params as any)?.username || '';
  const mfaStatus = (route.params as any)?.mfaStatus || 'TOTP_REQUIRED';

  // Event Handlers
  // Handles OTP input changes and clears errors
  const handleOtpChange = (text: string) => {
    // Remove any whitespace and convert to uppercase for consistency
    const cleanedText = text.replace(/\s/g, '').toUpperCase();
    setOtp(cleanedText);

    // Clear any error or success messages when user starts typing
    if (otpError) {
      setOtpError('');
    }
    if (verificationError) {
      setVerificationError('');
    }
  };

  // Handles TFA type change
  const handleTfaTypeChange = (type: TFAType) => {
    setTfaType(type);
    setOtp('');
    setOtpError('');
    setVerificationError('');
  };

  // Handles sending email OTP
  const handleSendEmailOTP = async () => {
    setIsSendingEmail(true);
    setVerificationError('');

    try {
      const response = await authServices.sendTFAEmail(username);

      if (response.success) {
        setVerificationError('OTP has been sent to your email');
      } else {
        setVerificationError(response.error || 'Failed to send OTP email');
      }
    } catch (error: any) {
      console.error('Send email OTP error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
    } finally {
      setIsSendingEmail(false);
    }
  };

  // Handles the TFA verification with validation
  const handleVerifyTFA = async () => {
    // Clear previous errors
    setOtpError('');
    setVerificationError('');

    // Validate OTP
    if (!otp) {
      setOtpError('Please enter the verification code');
      return;
    }

    if (tfaType === 'totp' && otp.length !== 6) {
      setOtpError('Please enter a valid 6-digit TOTP code');
      return;
    }

    if (tfaType === 'email' && otp.length !== 5) {
      setOtpError('Please enter a valid 5-character email OTP code');
      return;
    }

    // Attempt verification
    setIsLoading(true);
    try {
      // Add 1.5 second delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1500));

      console.log('🔍 [TFAVerificationScreen] Sending TFA verification request:', { username, tfaType, otp });

      let response;
      if (tfaType === 'totp') {
        response = await authServices.verifyTOTP(username, otp);
      } else {
        response = await authServices.verifyTFAEmail(username, otp);
      }

      console.log('📋 [TFAVerificationScreen] TFA verification response:', response);

      if (response.success && response.data) {
        // Verification successful - user is now logged in automatically
        console.log('✅ [TFAVerificationScreen] TFA verification successful, user logged in');

        // Update auth context
        await checkAuthStatus();

        // Show success message
        setVerificationError('Two-factor authentication successful! Redirecting to chat...');

        // Redirect to main app after successful verification
        setTimeout(() => {
          (navigation as any).navigate('app/main');
        }, 2000); // Show success message for 2 seconds then navigate

      } else {
        // Handle specific error messages from backend
        const errorMessage = response.error || 'Invalid verification code. Please try again.';
        console.error('TFA verification failed:', errorMessage);
        setVerificationError(errorMessage);
        setIsLoading(false); // Stop loading on error
      }
    } catch (error: any) {
      console.error('TFA verification error:', error);
      setVerificationError('Network error. Please check your connection and try again.');
      setIsLoading(false); // Stop loading on network error
    }
  };

  // Render Methods
  // Renders the TFA type selector
  const renderTfaTypeSelector = () => (
    <View className="mb-6">
      <Text className={`text-sm font-medium mb-3 ${theme.colors.text}`}>
        Choose Verification Method
      </Text>
      <View className="flex-row gap-3">
        <TouchableOpacity
          className={`flex-1 py-3 px-4 rounded-lg border ${tfaType === 'totp'
              ? `${theme.colors.primary} border-blue-500`
              : `${theme.colors.surface} ${theme.colors.border} border`
            }`}
          onPress={() => handleTfaTypeChange('totp')}
        >
          <Text className={`text-center font-medium ${tfaType === 'totp' ? 'text-white' : theme.colors.text
            }`}>
            TOTP
          </Text>
          <Text className={`text-xs text-center mt-1 ${tfaType === 'totp' ? 'text-blue-100' : theme.colors.textSecondary
            }`}>
            Google Authenticator
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          className={`flex-1 py-3 px-4 rounded-lg border ${tfaType === 'email'
              ? `${theme.colors.primary} border-blue-500`
              : `${theme.colors.surface} ${theme.colors.border} border`
            }`}
          onPress={() => handleTfaTypeChange('email')}
        >
          <Text className={`text-center font-medium ${tfaType === 'email' ? 'text-white' : theme.colors.text
            }`}>
            Email OTP
          </Text>
          <Text className={`text-xs text-center mt-1 ${tfaType === 'email' ? 'text-blue-100' : theme.colors.textSecondary
            }`}>
            Email Code
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  // Renders the OTP input field with validation error display
  const renderOtpField = () => (
    <View className="mb-6">
      <Text className={`text-sm font-medium mb-2 ${theme.colors.text}`}>
        {tfaType === 'totp' ? 'Enter TOTP Code' : 'Enter Email OTP Code'}
      </Text>
      {tfaType === 'totp' && (
        <Text className={`text-xs mb-3 ${theme.colors.textSecondary}`}>
          Enter the 6-digit code from your Google Authenticator or Authy app
        </Text>
      )}
      <TextInput
        className={`${theme.colors.surface} ${theme.colors.border} border rounded-lg px-4 py-3 ${theme.colors.text} text-center text-xl ${otpError ? 'border-red-500' : ''}`}
        placeholder={tfaType === 'totp' ? '000000' : 'Enter 5-character OTP'}
        placeholderTextColor={theme.isDark ? '#9CA3AF' : '#6B7280'}
        value={otp}
        onChangeText={handleOtpChange}
        keyboardType={tfaType === 'totp' ? 'numeric' : 'default'}
        autoCapitalize={tfaType === 'totp' ? 'none' : 'characters'}
        maxLength={tfaType === 'totp' ? 6 : 5}
        style={{
          textAlignVertical: 'center',
          letterSpacing: tfaType === 'totp' ? 4 : 0
        }}
      />
      {otpError ? (
        <Text className="text-red-500 text-sm mt-1 ml-1 text-center">{otpError}</Text>
      ) : null}
    </View>
  );

  // Renders the verify button with loading state
  const renderVerifyButton = () => (
    <TouchableOpacity
      className={`${theme.colors.primary} rounded-lg py-4 mb-4 flex-row items-center justify-center`}
      onPress={handleVerifyTFA}
      disabled={isLoading}
    >
      {isLoading ? (
        <>
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
          <Text className="text-white text-center font-semibold text-lg">
            Verifying...
          </Text>
        </>
      ) : (
        <Text className="text-white text-center font-semibold text-lg">
          Verify
        </Text>
      )}
    </TouchableOpacity>
  );

  // Renders the email OTP section
  const renderEmailOtpSection = () => {
    if (tfaType !== 'email') return null;

    return (
      <View className="mb-4">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg border ${theme.colors.surface} ${theme.colors.border} border`}
          onPress={handleSendEmailOTP}
          disabled={isSendingEmail}
        >
          <View className="flex-row items-center justify-center">
            {isSendingEmail ? (
              <>
                <ActivityIndicator size="small" color={theme.colors.text} style={{ marginRight: 8 }} />
                <Text className={`${theme.colors.text} font-medium`}>
                  Sending...
                </Text>
              </>
            ) : (
              <>
                <Ionicons name="mail-outline" size={20} color={theme.colors.text} style={{ marginRight: 8 }} />
                <Text className={`${theme.colors.text} font-medium`}>
                  Send Email OTP
                </Text>
              </>
            )}
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  // Main Render
  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <ScrollView
          style={{ flex: 1 }}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        >
          {/* Background Image Section */}
          <View style={{ height: height * 0.26 }}>
            <ImageBackground
              source={require('../../assets/images/auth/auth_bg.png')}
              style={{ flex: 1 }}
              resizeMode="cover"
            />
          </View>

          {/* TFA Verification Form Container */}
          <View className={`${theme.colors.surface} rounded-t-3xl px-6 pt-8 pb-8 shadow-md`} style={{ flex: 1, marginTop: -30 }}>
            {/* Welcome Header */}
            <View className="mb-4">
              <Text className={`text-3xl font-bold text-start mb-2 ${theme.colors.text}`}>
                Two-Factor Authentication
              </Text>
              <Text className={`text-start ${theme.colors.textSecondary}`}>
                Complete your login with additional verification
              </Text>
            </View>

            {/* TFA Type Selector */}
            {renderTfaTypeSelector()}

            {/* Form Fields */}
            {renderOtpField()}

            {/* Email OTP Section */}
            {renderEmailOtpSection()}

            {/* Verification Message Display */}
            {verificationError ? (
              <View className="mb-4">
                <Text className={`text-sm text-center ${verificationError.includes('successful') ||
                    verificationError.includes('sent')
                    ? 'text-green-600'
                    : 'text-red-600'
                  }`}>
                  {verificationError}
                </Text>
              </View>
            ) : null}

            {/* Verify Button */}
            {renderVerifyButton()}

            {/* Back to Login Link */}
            <View className="flex-row justify-center">
              <TouchableOpacity onPress={() => navigation.goBack()}>
                <Text className="text-blue-500 font-medium">
                  Back to Login
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default TFAVerificationScreen;
