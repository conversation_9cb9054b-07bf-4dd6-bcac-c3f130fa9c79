import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  TouchableOpacity, 
  SafeAreaView, 
  ScrollView, 
  ActivityIndicator, 
  Alert, 
  TextInput,
  Image,
  Dimensions
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import Ionicons from 'react-native-vector-icons/Ionicons';
import authServices from '../../services/authServices';

const { width } = Dimensions.get('window');

interface TOTPData {
  secret: string;
  url: string;
  qr: string;
}

const TOTPSetupScreen: React.FC = () => {
  // State
  const [totpData, setTotpData] = useState<TOTPData | null>(null);
  const [verificationCode, setVerificationCode] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isVerifying, setIsVerifying] = useState(false);
  const [step, setStep] = useState<'loading' | 'setup' | 'verification'>('loading');
  
  // Navigation and context
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { checkAuthStatus, user } = useAuth();

  // Load TOTP secret on component mount
  useEffect(() => {
    generateTOTPSecret();
  }, []);

  // Generate TOTP secret from backend
  const generateTOTPSecret = async () => {
    try {
      setIsLoading(true);
      console.log('🔍 [TOTPSetupScreen] Generating TOTP secret...');
      
      const response = await authServices.generateTOTPSecret();
      
      console.log('📋 [TOTPSetupScreen] Generate TOTP Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success && response.data) {
        console.log('✅ [TOTPSetupScreen] TOTP secret generated successfully');
        setTotpData(response.data);
        setStep('setup');
      } else {
        console.error('❌ [TOTPSetupScreen] Failed to generate TOTP:', response.error);
        Alert.alert('Error', response.error || 'Failed to generate TOTP secret');
        navigation.goBack();
      }
    } catch (error) {
      console.error('💥 [TOTPSetupScreen] Generate TOTP error:', error);
      Alert.alert('Error', 'Network error. Please try again.');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  // Verify TOTP code
  const verifyTOTPCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      Alert.alert('Error', 'Please enter a valid 6-digit verification code');
      return;
    }

    try {
      setIsVerifying(true);
      setStep('verification');
      
      console.log('🔍 [TOTPSetupScreen] Verifying TOTP code...');
      console.log('🔍 [TOTPSetupScreen] User:', user);
      console.log('🔍 [TOTPSetupScreen] Verification code:', verificationCode);
      
      // Get current user from auth context
      if (!user) {
        Alert.alert('Error', 'User not found. Please log in again.');
        return;
      }

      if (!totpData) {
        Alert.alert('Error', 'TOTP data not found. Please try again.');
        setStep('setup');
        return;
      }
      
      console.log('🔍 [TOTPSetupScreen] Calling verifyTOTPSetup with secret and token');
      const response = await authServices.verifyTOTPSetup(totpData.secret, verificationCode);
      
      console.log('📋 [TOTPSetupScreen] Verify TOTP Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success && response.data) {
        console.log('✅ [TOTPSetupScreen] TOTP verification successful');
        
        // Save the TOTP secret to backend
        const saveResponse = await authServices.saveTOTPSecret(totpData.secret);
        if (!saveResponse.success) {
          console.error('❌ [TOTPSetupScreen] Failed to save TOTP secret:', saveResponse.error);
          Alert.alert('Error', 'Verification successful but failed to save TOTP secret. Please try again.');
          setStep('setup');
          return;
        }
        
        // Update auth context
        await checkAuthStatus();
        
        Alert.alert(
          'Success', 
          'TOTP setup completed successfully! You can now use Google Authenticator for two-factor authentication.',
          [
            {
              text: 'OK',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        console.error('❌ [TOTPSetupScreen] TOTP verification failed:', response.error);
        setStep('setup');
        Alert.alert('Error', response.error || 'Invalid verification code. Please try again.');
      }
    } catch (error) {
      console.error('💥 [TOTPSetupScreen] Verify TOTP error:', error);
      setStep('setup');
      Alert.alert('Error', 'Network error. Please try again.');
    } finally {
      setIsVerifying(false);
    }
  };

  // Render Methods
  const renderHeader = () => (
    <View className="flex-row items-center mb-6">
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        className="mr-4 p-2"
      >
        <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
      </TouchableOpacity>
      <Text className={`text-2xl font-bold ${theme.colors.text}`}>
        Setup TOTP
      </Text>
    </View>
  );

  const renderLoading = () => (
    <View className="flex-1 justify-center items-center">
      <ActivityIndicator size="large" color={theme.colors.primary} />
      <Text className={`mt-4 ${theme.colors.textSecondary}`}>
        Generating TOTP secret...
      </Text>
    </View>
  );

  const renderSetup = () => (
    <ScrollView 
      style={{ flex: 1 }}
      showsVerticalScrollIndicator={false}
      contentContainerStyle={{ padding: 16 }}
    >
      {renderHeader()}
      
      {/* Instructions */}
      <View className={`mb-6 p-4 rounded-lg bg-blue-50 border border-blue-200`}>
        <View className="flex-row items-start mb-2">
          <Ionicons name="information-circle" size={20} color="#3B82F6" style={{ marginRight: 8, marginTop: 2 }} />
          <Text className="text-sm font-medium text-blue-800">
            Setup Instructions
          </Text>
        </View>
        <Text className="text-sm text-blue-700">
          1. Open Google Authenticator or Authy{'\n'}
          2. Scan the QR code below{'\n'}
          3. Or manually enter the secret key{'\n'}
          4. Enter the 6-digit code from your app
        </Text>
      </View>

      {/* QR Code */}
      {totpData && (
        <View className={`mb-6 p-4 rounded-lg ${theme.colors.surface} border ${theme.colors.border} items-center`}>
          <Text className={`text-lg font-semibold ${theme.colors.text} mb-4`}>
            Scan QR Code
          </Text>
          <Image 
            source={{ uri: totpData.qr }} 
            style={{ width: 200, height: 200 }}
            resizeMode="contain"
          />
        </View>
      )}

      {/* Secret Key */}
      {totpData && (
        <View className={`mb-6 p-4 rounded-lg ${theme.colors.surface} border ${theme.colors.border}`}>
          <Text className={`text-lg font-semibold ${theme.colors.text} mb-4`}>
            Manual Entry
          </Text>
          <Text className={`text-sm ${theme.colors.textSecondary} mb-2`}>
            If you can't scan the QR code, manually enter this secret key in your authenticator app:
          </Text>
          <View className={`p-3 rounded-lg bg-gray-100 border border-gray-300`}>
            <Text className="font-mono text-sm text-gray-800 text-center" style={{ color: '#1F2937' }}>
              {totpData.secret}
            </Text>
          </View>
        </View>
      )}

      {/* Verification Code Input */}
      <View className={`mb-6 p-4 rounded-lg ${theme.colors.surface} border ${theme.colors.border}`}>
        <Text className={`text-lg font-semibold ${theme.colors.text} mb-4`}>
          Verify Setup
        </Text>
        <Text className={`text-sm ${theme.colors.textSecondary} mb-4`}>
          Enter the 6-digit code from your authenticator app to complete the setup:
        </Text>
        
        <TextInput
          value={verificationCode}
          onChangeText={setVerificationCode}
          placeholder="Enter 6-digit code"
          placeholderTextColor="#9CA3AF"
          keyboardType="numeric"
          maxLength={6}
          className={`p-4 rounded-lg border ${theme.colors.border}`}
          style={{ 
            fontSize: 18, 
            textAlign: 'center', 
            letterSpacing: 4,
            backgroundColor: 'white',
            color: '#1F2937',
            borderColor: theme.colors.border
          }}
        />
        
        <TouchableOpacity
          className={`mt-4 py-3 px-4 rounded-lg ${theme.colors.primary} flex-row items-center justify-center`}
          onPress={verifyTOTPCode}
          disabled={verificationCode.length !== 6}
        >
          <Ionicons name="checkmark" size={20} color="white" style={{ marginRight: 8 }} />
          <Text className="text-white font-semibold">
            Verify & Complete Setup
          </Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );

  const renderVerification = () => (
    <View className="flex-1 justify-center items-center px-8">
      <ActivityIndicator size="large" color={theme.colors.primary} />
      <Text className={`mt-4 text-lg ${theme.colors.text} text-center`}>
        Verifying TOTP code...
      </Text>
      <Text className={`mt-2 text-sm ${theme.colors.textSecondary} text-center`}>
        Please wait while we verify your setup
      </Text>
    </View>
  );

  // Main Render
  if (isLoading) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        {renderLoading()}
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      {step === 'verification' ? renderVerification() : renderSetup()}
    </SafeAreaView>
  );
};

export default TOTPSetupScreen;
