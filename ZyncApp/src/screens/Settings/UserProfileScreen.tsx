import React, { useState, useEffect } from 'react';
import { View, Text, SafeAreaView, TouchableOpacity, Alert, TextInput, ScrollView, Switch, StatusBar, Dimensions, Platform } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useAuth } from '../../context/AuthContext';
import { useTheme } from '../../context/ThemeContext';

const UserProfileScreen: React.FC = () => {
  const { theme, toggleTheme } = useTheme();
  const { user, logout } = useAuth();
  const navigation = useNavigation();
  
  const [name, setName] = useState('');
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [headerHeight, setHeaderHeight] = useState(0);
  const [screenHeight, setScreenHeight] = useState(0);

  useEffect(() => {
    const calculateHeaderHeight = () => {
      const { height } = Dimensions.get('window');
      const statusBarHeight = Platform.OS === 'ios' ? 0 : StatusBar.currentHeight || 24; // iOS SafeAreaView handles status bar
      const headerContentHeight = 60; // Consistent height for both platforms
      const totalHeaderHeight = statusBarHeight + headerContentHeight;
      
      setScreenHeight(height);
      setHeaderHeight(totalHeaderHeight);
    };

    calculateHeaderHeight();
    
    // Listen for orientation changes
    const subscription = Dimensions.addEventListener('change', calculateHeaderHeight);
    
    return () => subscription?.remove();
  }, []);

  useEffect(() => {
    if (user) {
      setName(user.name || '');
      setUsername(user.username || '');
      setEmail(user.email || '');
    }
  }, [user]);

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
            } catch (error) {
              console.error('Logout error:', error);
            }
          },
        },
      ]
    );
  };

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <StatusBar 
        barStyle={theme.isDark ? 'light-content' : 'dark-content'}
        backgroundColor={theme.isDark ? '#000000' : '#FFFFFF'}
      />
      {/* Dynamic Header */}
      <View 
        className={`px-6 ${theme.colors.background}`}
        style={{ 
          height: Platform.OS === 'ios' ? 60 : 'auto',
          paddingTop: Platform.OS === 'ios' ? 0 : StatusBar.currentHeight || 24,
          paddingBottom: Platform.OS === 'ios' ? 0 : 12
        }}
      >
        <Text className={`text-2xl font-bold ${theme.colors.text}`}>
          Profile
        </Text>
      </View>

      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <View className="px-6">
          {/* Profile Header */}
          <View className="items-center mb-3">
            <View className={`w-28 h-28 rounded-full ${theme.colors.primary} items-center justify-center mb-6 shadow-lg`}>
              <Text className="text-white text-4xl font-bold">
                {name?.charAt(0)}{username?.charAt(0)}
              </Text>
            </View>
          </View>

          {/* Profile Form */}
          <View className={`rounded-2xl shadow-lg mb-1`}>
            
            {/* Name Input */}
            <View className="mb-4">
              <Text className={`text-sm font-semibold ${theme.colors.text} mb-3 uppercase tracking-wide`}>
                Full Name
              </Text>
              <View className={`border-2 ${theme.colors.border} rounded-xl overflow-hidden opacity-60`}>
                <TextInput
                  className={`${theme.colors.surface} px-5 py-4 ${theme.colors.textSecondary} text-base font-medium`}
                  placeholder="Enter full name"
                  placeholderTextColor={theme.isDark ? '#6B7280' : '#9CA3AF'}
                  value={name}
                  editable={false}
                  autoCapitalize="words"
                  autoCorrect={false}
                />
              </View>
            </View>

            {/* Username Input */}
            <View className="mb-4">
              <Text className={`text-sm font-semibold ${theme.colors.text} mb-3 uppercase tracking-wide`}>
                Username
              </Text>
              <View className={`border-2 ${theme.colors.border} rounded-xl overflow-hidden opacity-60`}>
                <TextInput
                  className={`${theme.colors.surface} px-5 py-4 ${theme.colors.textSecondary} text-base font-medium`}
                  placeholder="Enter username"
                  placeholderTextColor={theme.isDark ? '#6B7280' : '#9CA3AF'}
                  value={username}
                  editable={false}
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>

            {/* Email Input */}
            <View className="mb-6"
            
            >
              <Text className={`text-sm font-semibold ${theme.colors.text} mb-3 uppercase tracking-wide`}>
                Email Address
              </Text>
              <View className={`border-2 ${theme.colors.border} rounded-xl overflow-hidden opacity-60`}>
                <TextInput
                  className={`${theme.colors.surface} px-5 py-4 ${theme.colors.textSecondary} text-base font-medium`}
                  placeholder="Enter email address"
                  placeholderTextColor={theme.isDark ? '#6B7280' : '#9CA3AF'}
                  value={email}
                  editable={false}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
              </View>
            </View>
          </View>

          {/* Dark Mode Toggle - Commented Out */}
          {/* <View className={`${theme.colors.surface} rounded-2xl p-6 shadow-lg mb-6`}>
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className={`w-10 h-10 rounded-lg bg-orange-100 dark:bg-orange-900 items-center justify-center mr-4`}>
                  <Ionicons 
                    name={theme.isDark ? "moon" : "sunny"} 
                    size={20} 
                    color={theme.isDark ? '#FBBF24' : '#F59E0B'} 
                  />
                </View>
                <View>
                  <Text className={`text-base font-medium ${theme.colors.text}`}>
                    Dark Mode
                  </Text>
                  <Text className={`text-sm ${theme.colors.textSecondary}`}>
                    {theme.isDark ? 'Dark theme enabled' : 'Light theme enabled'}
                  </Text>
                </View>
              </View>
              <Switch
                value={theme.isDark}
                onValueChange={toggleTheme}
                trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
                thumbColor={theme.isDark ? '#FFFFFF' : '#FFFFFF'}
              />
            </View>
          </View> */}

          {/* TFA Settings */}
          <TouchableOpacity
            className={`${theme.colors.surface} rounded-2xl p-6 shadow-lg mb-6`}
            onPress={() => (navigation as any).navigate('settings/tfa')}
          
          >
            <View className="flex-row items-center justify-between">
              <View className="flex-row items-center">
                <View className={`w-10 h-10 rounded-lg bg-blue-100 dark:bg-blue-900 items-center justify-center mr-4`}>
                  <Ionicons 
                    name="shield-checkmark" 
                    size={20} 
                    color="#3B82F6" 
                  />
                </View>
                <View>
                  <Text className={`text-base font-medium ${theme.colors.text}`}>
                    Two-Factor Authentication
                  </Text>
                  <Text className={`text-sm ${theme.colors.textSecondary}`}>
                    Secure your account with 2FA
                  </Text>
                </View>
              </View>
              <Ionicons name="chevron-forward" size={20} color={theme.colors.textSecondary} />
            </View>
          </TouchableOpacity>

          {/* Logout Button */}
          <TouchableOpacity
            className={`bg-red-500 rounded-xl py-4 shadow-lg border border-red-400`}
            onPress={handleLogout}
          >
            <View className="flex-row items-center justify-center">
              <Ionicons name="log-out-outline" size={20} color="white" style={{ marginRight: 8 }} />
              <Text className="text-white text-center font-bold text-lg">
                Logout
              </Text>
            </View>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default UserProfileScreen;