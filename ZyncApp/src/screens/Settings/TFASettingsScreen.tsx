import React, { useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, SafeAreaView, ScrollView, ActivityIndicator, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../context/ThemeContext';
import { useAuth } from '../../context/AuthContext';
import Ionicons from 'react-native-vector-icons/Ionicons';
import authServices from '../../services/authServices';
// ZYNC 

// Main TFA Settings Component
const TFASettingsScreen: React.FC = () => {
  // State
  const [tfaStatus, setTfaStatus] = useState<{ TFARequire: boolean; TOTPActive: boolean }>({
    TFARequire: false,
    TOTPActive: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Navigation and context
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { checkAuthStatus } = useAuth();

  // Load TFA status on component mount and when screen comes into focus
  useEffect(() => {
    loadTFAStatus();
  }, []);

  // Refresh TFA status when returning from TOTP setup
  useEffect(() => {
    const unsubscribe = navigation.addListener('focus', () => {
      loadTFAStatus();
    });

    return unsubscribe;
  }, [navigation]);

  // Load TFA status from backend
  const loadTFAStatus = async () => {
    setIsLoading(true);
    try {
      console.log('🔍 [TFASettingsScreen] Loading TFA status...');
      const response = await authServices.getTFAStatus();
      
      console.log('📋 [TFASettingsScreen] TFA Status Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success && response.data) {
        console.log('✅ [TFASettingsScreen] Setting TFA status:', response.data);
        setTfaStatus(response.data);
      } else {
        console.error('❌ [TFASettingsScreen] Failed to load TFA status:', response.error);
      }
    } catch (error) {
      console.error('💥 [TFASettingsScreen] Load TFA status error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle TFA enable/disable
  const handleToggleTFA = async () => {
    Alert.alert(
      tfaStatus.TFARequire ? 'Disable Two-Factor Authentication' : 'Enable Two-Factor Authentication',
      tfaStatus.TFARequire 
        ? 'Are you sure you want to disable two-factor authentication? This will make your account less secure.'
        : 'Enabling two-factor authentication will add an extra layer of security to your account.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: tfaStatus.TFARequire ? 'Disable' : 'Enable',
          style: tfaStatus.TFARequire ? 'destructive' : 'default',
          onPress: async () => {
            setIsUpdating(true);
            try {
              const newTFAStatus = !tfaStatus.TFARequire;
              console.log('🔄 [TFASettingsScreen] Toggling TFA status to:', newTFAStatus);
              
              const response = await authServices.setTFA(newTFAStatus);
              
              console.log('📋 [TFASettingsScreen] Set TFA Response:', {
                success: response.success,
                data: response.data,
                error: response.error
              });
              
              if (response.success) {
                console.log('✅ [TFASettingsScreen] TFA status updated successfully');
                setTfaStatus(prev => ({ ...prev, TFARequire: newTFAStatus }));
                Alert.alert(
                  'Success',
                  tfaStatus.TFARequire 
                    ? 'Two-factor authentication has been disabled.'
                    : 'Two-factor authentication has been enabled.'
                );
              } else {
                console.error('❌ [TFASettingsScreen] Failed to update TFA:', response.error);
                Alert.alert('Error', response.error || 'Failed to update TFA settings');
              }
            } catch (error) {
              console.error('💥 [TFASettingsScreen] Toggle TFA error:', error);
              Alert.alert('Error', 'Network error. Please try again.');
            } finally {
              setIsUpdating(false);
            }
          },
        },
      ]
    );
  };



  // Render Methods
  const renderHeader = () => (
    <View className="flex-row items-center mb-6">
      <TouchableOpacity
        onPress={() => navigation.goBack()}
        className="mr-4 p-2"
      >
        <Ionicons name="arrow-back" size={24} color={theme.colors.text} />
      </TouchableOpacity>
      <Text className={`text-2xl font-bold ${theme.colors.text}`}>
        Two-Factor Authentication
      </Text>
    </View>
  );

  const renderTFAStatus = () => (
    <View className={`mb-6 p-4 rounded-lg ${theme.colors.surface} border ${theme.colors.border}`}>
      <View className="flex-row items-center justify-between mb-4">
        <Text className={`text-lg font-semibold ${theme.colors.text}`}>
          TFA Status
        </Text>
        <View className={`px-3 py-1 rounded-full ${
          tfaStatus.TFARequire ? 'bg-green-100' : 'bg-gray-100'
        }`}>
          <Text className={`text-sm font-medium ${
            tfaStatus.TFARequire ? 'text-green-800' : 'text-gray-600'
          }`}>
            {tfaStatus.TFARequire ? 'Enabled' : 'Disabled'}
          </Text>
        </View>
      </View>
      
      <Text className={`text-sm ${theme.colors.textSecondary} mb-4`}>
        Two-factor authentication adds an extra layer of security to your account by requiring a verification code in addition to your password.
      </Text>
      
      <TouchableOpacity
        className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
          tfaStatus.TFARequire 
            ? 'bg-red-500' 
            : theme.colors.primary
        }`}
        onPress={handleToggleTFA}
        disabled={isUpdating}
      >
        {isUpdating ? (
          <ActivityIndicator size="small" color="white" style={{ marginRight: 8 }} />
        ) : (
          <Ionicons 
            name={tfaStatus.TFARequire ? "shield-checkmark" : "shield-outline"} 
            size={20} 
            color="white" 
            style={{ marginRight: 8 }} 
          />
        )}
        <Text className="text-white font-semibold">
          {isUpdating ? 'Updating...' : (tfaStatus.TFARequire ? 'Disable TFA' : 'Enable TFA')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderTOTPSection = () => (
    <View className={`mb-6 p-4 rounded-lg ${theme.colors.surface} border ${theme.colors.border}`}>
      <View className="flex-row items-center justify-between mb-4">
        <Text className={`text-lg font-semibold ${theme.colors.text}`}>
          TOTP (Google Authenticator)
        </Text>
        <View className={`px-3 py-1 rounded-full ${
          tfaStatus.TOTPActive ? 'bg-blue-100' : 'bg-gray-100'
        }`}>
          <Text className={`text-sm font-medium ${
            tfaStatus.TOTPActive ? 'text-blue-800' : 'text-gray-600'
          }`}>
            {tfaStatus.TOTPActive ? 'Active' : 'Inactive'}
          </Text>
        </View>
      </View>
      
      <Text className={`text-sm ${theme.colors.textSecondary} mb-4`}>
        TOTP allows you to use apps like Google Authenticator or Authy to generate verification codes.
      </Text>
      
      <View className="flex-row gap-3">

        
        <TouchableOpacity
          className={`flex-1 py-3 px-4 rounded-lg ${
            tfaStatus.TOTPActive ? 'bg-blue-500' : theme.colors.primary
          }`}
          onPress={() => {
            if (tfaStatus.TOTPActive) {
              // If TOTP is already active, show update option
              Alert.alert(
                'Update TOTP',
                'Do you want to update your TOTP setup? This will invalidate your current authenticator app.',
                [
                  { text: 'Cancel', style: 'cancel' },
                  { text: 'Update', onPress: () => (navigation as any).navigate('settings/totp-setup') }
                ]
              );
            } else {
              // Navigate to TOTP setup screen
              (navigation as any).navigate('settings/totp-setup');
            }
          }}
          disabled={isUpdating}
        >
          <View className="flex-row items-center justify-center">
            <Ionicons 
              name={tfaStatus.TOTPActive ? "refresh" : "add"} 
              size={20} 
              color="white" 
              style={{ marginRight: 8 }} 
            />
            <Text className="text-white font-semibold">
              {tfaStatus.TOTPActive ? 'Update' : 'Setup'}
            </Text>
          </View>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderEmailOTPSection = () => (
    <View className={`mb-6 p-4 rounded-lg ${theme.colors.surface} border ${theme.colors.border}`}>
      <View className="flex-row items-center justify-between mb-4">
        <Text className={`text-lg font-semibold ${theme.colors.text}`}>
          Email OTP
        </Text>
        <View className="px-3 py-1 rounded-full bg-green-100">
          <Text className="text-sm font-medium text-green-800">
            Always Available
          </Text>
        </View>
      </View>
      
      <Text className={`text-sm ${theme.colors.textSecondary} mb-4`}>
        Email OTP sends a verification code to your registered email address. This option is always available when TFA is enabled.
      </Text>
      
      <View className="flex-row items-center">
        <Ionicons name="checkmark-circle" size={20} color="#10B981" style={{ marginRight: 8 }} />
        <Text className={`text-sm ${theme.colors.textSecondary}`}>
          Automatically configured with your account email
        </Text>
      </View>
    </View>
  );

  const renderSecurityInfo = () => (
    <View className={`p-4 rounded-lg bg-blue-50 border border-blue-200`}>
      <View className="flex-row items-start mb-2">
        <Ionicons name="information-circle" size={20} color="#3B82F6" style={{ marginRight: 8, marginTop: 2 }} />
        <Text className="text-sm font-medium text-blue-800">
          Security Information
        </Text>
      </View>
      <Text className="text-sm text-blue-700">
        • Two-factor authentication significantly improves your account security{'\n'}
        • You can use either TOTP or Email OTP for verification{'\n'}
        • TOTP codes are time-based and work offline{'\n'}
        • Email OTP codes expire after 5 minutes
      </Text>
    </View>
  );

  // Main Render
  if (isLoading) {
    return (
      <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
        <View className="flex-1 justify-center items-center">
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text className={`mt-4 ${theme.colors.textSecondary}`}>
            Loading TFA settings...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className={`flex-1 ${theme.colors.background}`}>
      <ScrollView 
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ padding: 16 }}
      >
        {renderHeader()}
        {renderTFAStatus()}
        {renderTOTPSection()}
        {renderEmailOTPSection()}
        {renderSecurityInfo()}
      </ScrollView>
    </SafeAreaView>
  );
};

export default TFASettingsScreen;
