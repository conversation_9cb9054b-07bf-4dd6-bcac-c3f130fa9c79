import apiController from './apiController';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  username: string;
  email: string;
  password: string;
  authProvider?: string;
}

export interface AuthResponse {
  message: string;
  second_message?: string;
  token: string;
  authExpireTime: string;
  user: {
    id: number;
    name: string;
    username: string;
    email: string;
    is_email_verified: boolean;
    isActive?: boolean;
    role_id: number;
  };
  otp?: string;
  otpExpireAt?: string;
  otpExpireTime?: string;
  email_sent?: boolean;
  is_active?: number;
  auth_provider?: string;
  MFAStatus?: string; // Added for TFA status
}

export interface OtpVerificationData {
  email: string;
  otp: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  email: string;
  otp: string;
  newPassword: string;
}

export interface ApiError {
  message: string;
  error?: string;
}

class AuthServices {
  private readonly TOKEN_KEY = 'auth_token';
  private readonly USER_KEY = 'user_data';

  // Login user
  public async login(credentials: LoginCredentials) {
    try {
      // Map email to username field for backend compatibility
      const loginData = {
        email: credentials.email,
        password: credentials.password,
        device: 'android' // Set device type for mobile app
      };

      console.log('🔍 [authServices] Sending login request:', { ...loginData, password: '[HIDDEN]' });
      const response = await apiController.post<AuthResponse>('/auth/login', loginData);
      console.log('📋 [authServices] Login response:', response);

      if (response.success && response.data) {
        // Handle successful login
        if (response.data.token) {
          console.log('✅ [authServices] Login successful with token');
          await this.storeAuthData(response.data);
          apiController.setAuthToken(response.data.token);
          return {
            success: true,
            data: response.data,
          };
        }

        // Handle inactive account case
        if (response.data.is_active === 0) {
          console.log('⚠️ [authServices] Account not activated');
          return {
            success: true,
            data: response.data,
          };
        }

        // Handle TFA cases
        if (response.data.MFAStatus) {
          console.log('🔐 [authServices] TFA required:', response.data.MFAStatus);
          return {
            success: true,
            data: response.data,
          };
        }
      }

      console.log('❌ [authServices] Login failed:', response.error);
      return {
        success: false,
        error: response.error || response.message || 'Login failed',
      };
    } catch (error: any) {
      console.error('🚨 [authServices] Login error:', error);

      // Handle backend error responses
      if (error.response?.data) {
        const responseData = error.response.data;

        // Check if this is an email verification case
        if (responseData.is_active === 0 && responseData.user) {
          return {
            success: true,
            data: responseData,
          };
        }

        // Check if this is an auth provider mismatch case
        if (responseData.auth_provider && responseData.is_active === 0) {
          return {
            success: true,
            data: responseData,
          };
        }
      }

      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Verify email before registration
  public async verifyEmailBeforeRegister(email: string) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/verify-email', { email });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Email verification failed',
        };
      }
    } catch (error: any) {
      console.error('Email verification error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Register user
  public async register(userData: RegisterData) {
    try {
      // Ensure the data matches backend expectations exactly
      const registerData = {
        name: userData.name,
        username: userData.username,
        email: userData.email,
        password: userData.password,
        authProvider: userData.authProvider || 'manual'
      };

      console.log('Sending registration data:', { ...registerData, password: '[HIDDEN]' });

      const response = await apiController.post<AuthResponse>('/auth/register', registerData);

      if (response.success && response.data) {
        // Registration successful, but user needs email verification
        // Don't store auth data yet since account is not active
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Registration failed',
        };
      }
    } catch (error: any) {
      console.error('Registration error:', error);
      
      // Handle specific backend error responses
      if (error.response?.data) {
        const responseData = error.response.data;
        
        // Handle specific backend error messages
        if (responseData.message) {
          return {
            success: false,
            error: responseData.message,
          };
        }
      }
      
      // Handle network or server errors
      if (error.response?.status === 500) {
        return {
          success: false,
          error: 'Server error. Please try again later.',
        };
      }
      
      if (error.response?.status === 400) {
        return {
          success: false,
          error: error.response.data?.message || 'Invalid registration data',
        };
      }
      
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Verify Registration OTP
  public async verifyRegisterOtp(otpData: OtpVerificationData) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/verify-register-otp', otpData);

      if (response.success && response.data) {
        // Store auth data after successful verification
        await this.storeAuthData(response.data);
        apiController.setAuthToken(response.data.token);
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'OTP verification failed',
        };
      }
    } catch (error: any) {
      console.error('OTP verification error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Regenerate Registration OTP
  public async regenerateRegisterOTP(email: string) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/regenerate-otp', { email });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to regenerate OTP',
        };
      }
    } catch (error: any) {
      console.error('Regenerate OTP error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Activate Account (for email verification flow)
  public async activeAccount(email: string) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/active-account', { email });

      if (response.success && response.data) {
        await this.storeAuthData(response.data);
        apiController.setAuthToken(response.data.token);
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to activate account',
        };
      }
    } catch (error: any) {
      console.error('Account activation error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Regenerate Forgot Password OTP (reuse forgot password endpoint)
  public async regenerateForgotPasswordOTP(email: string) {
    try {
      // Backend doesn't have separate regenerate endpoint, so we call forgot-password again
      const response = await apiController.post<AuthResponse>('/auth/forgot-password', { email });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to regenerate OTP',
        };
      }
    } catch (error: any) {
      console.error('Regenerate forgot password OTP error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Forgot password
  public async forgotPassword(data: ForgotPasswordData) {
    try {
      const response = await apiController.post<AuthResponse>('/auth/forgot-password', data);

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Failed to send reset email',
        };
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Note: Backend doesn't have separate OTP verification endpoint
  // This functionality is combined with password reset in verifyForgotPasswordOtp method

  // Verify Forgot Password OTP and Reset Password
  public async verifyForgotPasswordOtp(data: ResetPasswordData) {
    try {
      // Map to backend expected format
      const resetData = {
        email: data.email,
        token: data.otp,
        password: data.newPassword
      };

      const response = await apiController.post<AuthResponse>('/auth/reset-pwd', resetData);

      if (response.success && response.data) {
        // Password reset successful, user can now login
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || response.message || 'Password reset failed',
        };
      }
    } catch (error: any) {
      console.error('Reset password error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }



  // Logout user
  public async logout() {
    try {
      // Backend doesn't have logout endpoint, so just clear local data
      await this.clearAuthData();
      apiController.removeAuthToken();
      return {
        success: true,
      };
    } catch (error: any) {
      console.error('Logout error:', error);
      // Clear local data even if there's an error
      await this.clearAuthData();
      apiController.removeAuthToken();
      return {
        success: true, // Still return success since local data is cleared
      };
    }
  }

  // Note: Backend doesn't have refresh token endpoint, so we'll remove this method
  // If needed in the future, it can be added when backend supports it

  // Get current user
  public async getCurrentUser() {
    try {
      const userData = await AsyncStorage.getItem(this.USER_KEY);
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Check if user is authenticated
  public async isAuthenticated(): Promise<boolean> {
    try {
      const token = await AsyncStorage.getItem(this.TOKEN_KEY);
      return !!token;
    } catch (error) {
      console.error('Authentication check error:', error);
      return false;
    }
  }

  // Store authentication data
  private async storeAuthData(authData: AuthResponse) {
    try {
      // Validate authData before storing
      if (!authData || !authData.token) {
        console.error('Invalid authData provided to storeAuthData:', authData);
        throw new Error('Invalid authentication data');
      }

      const tokenValue = authData.token || '';
      const userValue = authData.user ? JSON.stringify(authData.user) : '{}';

      await AsyncStorage.multiSet([
        [this.TOKEN_KEY, tokenValue],
        [this.USER_KEY, userValue],
      ]);
    } catch (error) {
      console.error('Store auth data error:', error);
      throw error;
    }
  }

  // Clear authentication data
  private async clearAuthData() {
    try {
      await AsyncStorage.multiRemove([
        this.TOKEN_KEY,
        this.USER_KEY,
      ]);
    } catch (error) {
      console.error('Clear auth data error:', error);
      throw error;
    }
  }

  // Get stored token
  public async getStoredToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(this.TOKEN_KEY);
    } catch (error) {
      console.error('Get stored token error:', error);
      return null;
    }
  }

  // TFA (Two-Factor Authentication) Methods
  public async getTFAStatus() {
    try {
      console.log('🔍 [authServices] Getting TFA status from /auth/get-tfa-status...');
      const response = await apiController.get<{ data: { TFARequire: boolean; TOTPActive: boolean } }>('/auth/get-tfa-status');
      
      console.log('📋 [authServices] TFA Status API Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success && response.data) {
        // Handle nested data structure from backend
        const tfaData = response.data.data || response.data;
        console.log('✅ [authServices] TFA status retrieved successfully:', tfaData);
        return {
          success: true,
          data: tfaData,
        };
      } else {
        console.error('❌ [authServices] Failed to get TFA status:', response.error);
        return {
          success: false,
          error: response.error || 'Failed to get TFA status',
        };
      }
    } catch (error: any) {
      console.error('💥 [authServices] Get TFA status error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  public async setTFA(enable: boolean) {
    try {
      console.log('🔄 [authServices] Setting TFA status to:', enable, 'via /auth/set-tfa');
      const response = await apiController.post<{ success: boolean }>('/auth/set-tfa', { enable });
      
      console.log('📋 [authServices] Set TFA API Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success && response.data) {
        console.log('✅ [authServices] TFA status updated successfully');
        return {
          success: true,
          data: response.data,
        };
      } else {
        console.error('❌ [authServices] Failed to update TFA status:', response.error);
        return {
          success: false,
          error: response.error || 'Failed to update TFA settings',
        };
      }
    } catch (error: any) {
      console.error('💥 [authServices] Set TFA error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }





  public async sendTFAEmail(username: string) {
    try {
      const response = await apiController.post<{ success: boolean }>('/auth/send-tfa-email', { username });
      
      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
        };
      } else {
        return {
          success: false,
          error: response.error || 'Failed to send TFA email',
        };
      }
    } catch (error: any) {
      console.error('Send TFA email error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  public async verifyTFAEmail(username: string, token: string) {
    try {
      const response = await apiController.post<{ accessToken: string }>('/auth/verify-tfa-email', { 
        username, 
        token 
      });
      
      if (response.success && response.data) {
        // Backend returns { accessToken: string }, we need to construct AuthResponse
        if (response.data && response.data.accessToken) {
          // Set the token in API controller before fetching user profile
          apiController.setAuthToken(response.data.accessToken);
          
          // Get user data using the token
          const userResponse = await apiController.get<any>('/auth/get-profile');
          
          if (userResponse.success && userResponse.data) {
            const userData = userResponse.data.data || userResponse.data;
            const authData: AuthResponse = {
              message: 'TFA verification successful',
              token: response.data.accessToken,
              authExpireTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
              user: {
                id: userData.id,
                name: userData.name,
                username: userData.username,
                email: userData.email,
                is_email_verified: userData.is_email_verified || true,
                role_id: userData.role_id || 1
              }
            };
            
            // Store auth data (token is already set in API controller)
            await this.storeAuthData(authData);
            
            return {
              success: true,
              data: authData,
            };
          } else {
            console.error('Failed to get user profile:', userResponse.error);
            return {
              success: false,
              error: 'Failed to get user profile. Please try again.',
            };
          }
        } else {
          console.error('Invalid response structure:', response.data);
          return {
            success: false,
            error: 'Invalid response from server. Please try again.',
          };
        }
      } else {
        return {
          success: false,
          error: response.error || 'Failed to verify TFA email',
        };
      }
    } catch (error: any) {
      console.error('Verify TFA email error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  public async verifyTOTP(username: string, token: string) {
    try {
      const response = await apiController.post<{ accessToken: string }>('/auth/verify-totp', { 
        username, 
        token 
      });
      
      if (response.success && response.data) {
        // Backend returns { accessToken: string }, we need to construct AuthResponse
        if (response.data && response.data.accessToken) {
          // Set the token in API controller before fetching user profile
          apiController.setAuthToken(response.data.accessToken);
          
          // Get user data using the token
          const userResponse = await apiController.get<any>('/auth/get-profile');
          
          if (userResponse.success && userResponse.data) {
            const userData = userResponse.data.data || userResponse.data;
            const authData: AuthResponse = {
              message: 'TFA verification successful',
              token: response.data.accessToken,
              authExpireTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
              user: {
                id: userData.id,
                name: userData.name,
                username: userData.username,
                email: userData.email,
                is_email_verified: userData.is_email_verified || true,
                role_id: userData.role_id || 1
              }
            };
            
            // Store auth data (token is already set in API controller)
            await this.storeAuthData(authData);
            
            return {
              success: true,
              data: authData,
            };
          } else {
            console.error('Failed to get user profile:', userResponse.error);
            return {
              success: false,
              error: 'Failed to get user profile. Please try again.',
            };
          }
        } else {
          console.error('Invalid response structure:', response.data);
          return {
            success: false,
            error: 'Invalid response from server. Please try again.',
          };
        }
      } else {
        return {
          success: false,
          error: response.error || 'Failed to verify TOTP',
        };
      }
    } catch (error: any) {
      console.error('Verify TOTP error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  public async generateTOTPSecret() {
    try {
      console.log('🔍 [authServices] Generating TOTP secret...');
      const response = await apiController.post<{ data: { secret: string; url: string; qr: string } }>('/auth/generate-totp-secret');
      
      console.log('📋 [authServices] Generate TOTP Secret Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success && response.data) {
        const totpData = response.data.data || response.data;
        console.log('✅ [authServices] TOTP secret generated successfully:', totpData);
        return {
          success: true,
          data: totpData,
        };
      } else {
        console.error('❌ [authServices] Failed to generate TOTP secret:', response.error);
        return {
          success: false,
          error: response.error || 'Failed to generate TOTP secret',
        };
      }
    } catch (error: any) {
      console.error('💥 [authServices] Generate TOTP secret error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  public async verifyTOTPSetup(secret: string, token: string) {
    try {
      console.log('🔍 [authServices] Verifying TOTP setup...');
      const response = await apiController.post<{ success: boolean; message: string }>('/auth/verify-totp-setup', { 
        secret, 
        token 
      });
      
      console.log('📋 [authServices] Verify TOTP Setup Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success) {
        console.log('✅ [authServices] TOTP setup verification successful');
        return {
          success: true,
          data: response.data,
        };
      } else {
        console.error('❌ [authServices] Failed to verify TOTP setup:', response.error);
        return {
          success: false,
          error: response.error || 'Failed to verify TOTP setup',
        };
      }
    } catch (error: any) {
      console.error('💥 [authServices] Verify TOTP setup error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  public async saveTOTPSecret(secret: string) {
    try {
      console.log('🔍 [authServices] Saving TOTP secret...');
      const response = await apiController.post<{ success: boolean }>('/auth/set-totp-secret-tfa', { secret });
      
      console.log('📋 [authServices] Save TOTP Secret Response:', {
        success: response.success,
        data: response.data,
        error: response.error
      });
      
      if (response.success) {
        console.log('✅ [authServices] TOTP secret saved successfully');
        return {
          success: true,
        };
      } else {
        console.error('❌ [authServices] Failed to save TOTP secret:', response.error);
        return {
          success: false,
          error: response.error || 'Failed to save TOTP secret',
        };
      }
    } catch (error: any) {
      console.error('💥 [authServices] Save TOTP secret error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }

  // Enhanced login method with TFA support
  public async loginWithTFA(username: string, password: string) {
    try {
      const response = await apiController.post<AuthResponse | { MFAStatus: string }>('/auth/login', {
        username,
        password
      });

      if (response.success && response.data) {
        // Check if account is not activated (is_active: 0)
        if ('is_active' in response.data && response.data.is_active === 0) {
          // Account not activated, return the data for frontend to handle OTP verification
          return {
            success: true,
            data: response.data,
            requiresTFA: false,
            accountNotActivated: true,
          };
        }
        
        // Check if TFA is required
        if ('MFAStatus' in response.data) {
          // TFA is required, return the status for frontend to handle
          return {
            success: true,
            data: response.data,
            requiresTFA: true,
          };
        } else {
          // No TFA required, proceed with normal login
          const authData = response.data as AuthResponse;
          
          // Only store auth data if there's a valid token
          if (authData.token) {
            await this.storeAuthData(authData);
            apiController.setAuthToken(authData.token);
          }
          
          return {
            success: true,
            data: authData,
            requiresTFA: false,
          };
        }
      } else {
        return {
          success: false,
          error: response.error || 'Login failed',
        };
      }
    } catch (error: any) {
      console.error('Login with TFA error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Network error occurred',
      };
    }
  }
}

// Export singleton instance
export const authServices = new AuthServices();
export default authServices;
