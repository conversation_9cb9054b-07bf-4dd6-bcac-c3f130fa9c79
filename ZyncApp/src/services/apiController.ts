import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { Platform } from 'react-native';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

class ApiController {
  private instance: AxiosInstance;
  private baseURL: string;

  constructor() {
    // Platform-specific base URL configuration
    let defaultURL = 'http://localhost:3333';
    
    if (Platform.OS === 'android') {
      // Android emulator uses ******** to access host machine's localhost
      defaultURL = 'http://********:3333';
    }
    
    this.baseURL = process.env.BACKEND_API_URL || defaultURL;

    console.log('API Controller initialized with baseURL:', this.baseURL);

    this.instance = axios.create({
      baseURL: `${this.baseURL}/api`, // Add /api prefix for Zync_workspace backend
      timeout: 15000, // Increased timeout for better UX
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log('Full API base URL:', `${this.baseURL}/api`);

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor
    this.instance.interceptors.request.use(
      (config) => {
        // Add auth token if available
        const token = this.getAuthToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.instance.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`API Response: ${response.status} ${response.config.url}`);
        return response;
      },
      (error) => {
        console.error('Response Error:', error);
        
        if (error.response?.status === 401) {
          // Handle unauthorized access
          this.handleUnauthorized();
        }
        
        return Promise.reject(error);
      }
    );
  }

  private getAuthToken(): string | null {
    // Token is set via setAuthToken method when user logs in
    // The token is stored in the axios instance headers
    const authHeader = this.instance.defaults.headers.Authorization;
    if (typeof authHeader === 'string') {
      return authHeader.replace('Bearer ', '');
    }
    return null;
  }

  private handleUnauthorized(): void {
    // Handle unauthorized access (logout, redirect to login, etc.)
    console.log('Unauthorized access detected');
  }

  public setAuthToken(token: string): void {
    this.instance.defaults.headers.Authorization = `Bearer ${token}`;
  }

  public removeAuthToken(): void {
    delete this.instance.defaults.headers.Authorization;
  }

  // Generic HTTP methods
  public async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.get(url, config);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  public async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      console.log(`POST ${url}`, data ? { ...data, password: data.password ? '[HIDDEN]' : undefined } : 'No data');
      
      const response = await this.instance.post(url, data, config);
      
      console.log(`POST ${url} Response:`, response.data);
      
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      // For login endpoint, let the calling code handle 403 status
      if (url === '/auth/login' && error.response?.status === 403) {
        throw error; // Re-throw so authServices can handle it
      }
      
      console.error(`POST ${url} Error:`, {
        status: error.response?.status,
        data: error.response?.data,
        message: error.message
      });
      
      return this.handleError(error);
    }
  }

  public async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.put(url, data, config);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  public async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response = await this.instance.delete(url, config);
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return this.handleError(error);
    }
  }

  private handleError(error: any): ApiResponse {
    let message = 'An error occurred';

    if (error.response) {
      // Server responded with error status
      const status = error.response.status;
      const responseData = error.response.data;
      
      console.error('Server Error Details:', {
        status,
        data: responseData,
        url: error.config?.url
      });
      
      if (status === 500) {
        message = 'Server error. Please try again later.';
      } else if (status === 400) {
        message = responseData?.message || 'Invalid request data';
      } else if (status === 401) {
        message = responseData?.message || 'Invalid credentials';
      } else if (status === 403) {
        message = responseData?.message || 'Access forbidden';
      } else if (status === 404) {
        message = responseData?.message || 'Resource not found';
      } else if (status === 409) {
        message = responseData?.message || 'Conflict with existing data';
      } else {
        message = responseData?.message || error.response.statusText || `Server error: ${status}`;
      }
    } else if (error.request) {
      // Network error
      console.error('Network Error:', error.request);
      message = 'Network error. Please check your connection.';
    } else {
      // Other error
      console.error('Other Error:', error.message);
      message = error.message || 'An unexpected error occurred';
    }

    console.error('API Error Summary:', {
      message,
      status: error.response?.status,
      data: error.response?.data,
      url: error.config?.url,
    });

    return {
      success: false,
      error: message,
      message,
    };
  }

  // Test backend connectivity
  public async testConnection(): Promise<boolean> {
    try {
      console.log('Testing backend connection...');
      
      // Try to connect to the base API URL to see if server is reachable
      // We'll use a simple HEAD request to check if the server responds
      const testUrl = `${this.baseURL}/api`;
      console.log('Testing connection to:', testUrl);
      
      const response = await axios.head(testUrl, { 
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept any status < 500 as "server is reachable"
      });
      
      console.log('Backend connection successful:', response.status);
      return true;
    } catch (error: any) {
      console.error('Backend connection failed:', {
        status: error.response?.status,
        message: error.message,
        url: error.config?.url
      });
      
      // If we can't reach the server at all, return false
      return false;
    }
  }

  // File upload method
  public async uploadFile<T>(url: string, file: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.instance.post(url, formData, {
        ...config,
        headers: {
          ...config?.headers,
          'Content-Type': 'multipart/form-data',
        },
      });

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return this.handleError(error);
    }
  }
}

// Export singleton instance
export const apiController = new ApiController();
export default apiController;
