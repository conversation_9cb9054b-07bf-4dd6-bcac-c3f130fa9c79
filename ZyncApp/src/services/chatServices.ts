import { StreamChat } from 'stream-chat';
import userServices from './userServices';

export interface ChatUser {
  id: string;
  name: string;
  image?: string;
}

class ChatServices {
  private client: StreamChat | null = null;
  private currentUser: ChatUser | null = null;

  // Initialize Stream Chat client
  public async initializeChat(user: Chat<PERSON><PERSON>) {
    try {
      console.log('🚀 Initializing chat for user:', user);

      // Disconnect existing user if already connected
      if (this.client && this.currentUser) {
        console.log('🔄 Disconnecting existing user before connecting new one');
        await this.client.disconnectUser();
        this.currentUser = null;
      }

      // Get Stream Chat token from backend
      const tokenResponse = await userServices.getStreamChatToken();
      console.log('🔑 Token response:', tokenResponse);

      if (!tokenResponse.success) {
        throw new Error(tokenResponse.error || 'Failed to get chat token');
      }

      // The response structure is: {success: true, data: {data: {token, appKey, ...}}}
      // Because apiController wraps the backend response
      const actualData = (tokenResponse as any).data?.data || (tokenResponse as any).data;
      const token = actualData?.token;
      const appKey = actualData?.appKey;
      console.log('🔑 Token:', token);
      console.log('🔑 App Key:', appKey);

      if (!token) {
        throw new Error('User token can not be empty');
      }

      // Initialize Stream Chat client if not already done
      if (!this.client) {
        this.client = StreamChat.getInstance(appKey);
      }

      // Connect user
      await this.client.connectUser(
        {
          id: user.id,
          name: user.name,
          image: user.image,
        },
        token
      );

      this.currentUser = user;
      console.log('✅ Chat initialized successfully');
      return { success: true };
    } catch (error: any) {
      console.error('Initialize chat error:', error);
      return {
        success: false,
        error: error.message || 'Failed to initialize chat',
      };
    }
  }

  // Upsert user in Stream Chat
  private async upsertUser(userId: string, userName: string, userImage?: string) {
    if (!this.client) {
      throw new Error('Chat not initialized');
    }

    try {
      await this.client.upsertUser({
        id: userId,
        name: userName,
        image: userImage,
      });
      console.log('✅ User upserted successfully:', userId);
    } catch (error: any) {
      console.error('❌ Failed to upsert user:', userId, error);
      throw error;
    }
  }

  // Create or get existing channel with another user
  public async createDirectChannel(otherUserId: string, otherUserName: string, otherUserImage?: string) {
    if (!this.client || !this.currentUser) {
      throw new Error('Chat not initialized');
    }

    try {
      console.log('🔄 Creating channel with user:', otherUserId, otherUserName);

      // Ensure the other user exists in Stream Chat via backend
      const ensureUserResult = await userServices.ensureStreamChatUser(otherUserId);
      if (!ensureUserResult.success) {
        throw new Error(ensureUserResult.error || 'Failed to ensure other user exists in Stream Chat');
      }

      const channelId = [this.currentUser.id, otherUserId].sort().join('_');

      const channel = this.client.channel('messaging', channelId, {
        members: [this.currentUser.id, otherUserId],
      });

      await channel.create();
      console.log('✅ Channel created successfully:', channelId);
      return { success: true, channel };
    } catch (error: any) {
      console.error('❌ Create channel error:', error);
      return {
        success: false,
        error: error.message || 'Failed to create channel',
      };
    }
  }

  // Get the Stream Chat client instance
  public getClient() {
    return this.client;
  }

  // Get current user
  public getCurrentUser() {
    return this.currentUser;
  }

  // Disconnect from Stream Chat
  public async disconnect() {
    if (this.client) {
      await this.client.disconnectUser();
      this.client = null;
      this.currentUser = null;
    }
  }

  // Check if chat is initialized
  public isInitialized() {
    return this.client !== null && this.currentUser !== null;
  }
}

const chatServices = new ChatServices();
export default chatServices;
