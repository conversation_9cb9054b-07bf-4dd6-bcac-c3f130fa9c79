import apiController from './apiController';

export interface User {
  id: number;
  name: string;
  username: string;
  email: string;
  pic?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateUserData {
  name?: string;
  username?: string;
  email?: string;
}

// Note: User preferences are not implemented in the current backend
// This interface is kept for future implementation if needed
export interface UserPreferences {
  // Will be implemented when backend supports it
}

class UserServices {
  // Get current user profile
  public async getUserProfile(userId?: number) {
    try {
      if (userId) {
        // Get specific user info
        const response = await apiController.get<User>(`/auth/get-user-info?id=${userId}`);
        return response;
      } else {
        // Get current user profile
        const response = await apiController.get<User>('/auth/get-profile');
        return response;
      }
    } catch (error: any) {
      console.error('Get user profile error:', error);
      
      // Handle specific error cases
      if (error.response?.status === 401) {
        return {
          success: false,
          error: 'Authentication required. Please login again.',
        };
      }
      
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to get user profile',
      };
    }
  }

  // Update user profile
  public async updateUserProfile(userData: UpdateUserData) {
    try {
      // Note: Backend expects user ID in the request, but we'll use current user
      const response = await apiController.post<{ success: boolean }>('/auth/update-user-profile', userData);
      return response;
    } catch (error: any) {
      console.error('Update user profile error:', error);
      
      // Handle specific error cases
      if (error.response?.status === 400) {
        return {
          success: false,
          error: error.response.data?.message || 'Invalid profile data',
        };
      }
      
      if (error.response?.status === 401) {
        return {
          success: false,
          error: 'Authentication required. Please login again.',
        };
      }
      
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to update profile',
      };
    }
  }

  // Upload user avatar/thumbnail
  public async uploadAvatar(imageFile: any) {
    try {
      const response = await apiController.uploadFile<{ data: any }>('/auth/set-thumbnail', imageFile);
      return response;
    } catch (error: any) {
      console.error('Upload avatar error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to upload avatar',
      };
    }
  }

  // Note: Backend doesn't have separate delete avatar endpoint
  // Avatar can be updated by uploading a new one

  // Get all users (for admin/search purposes)
  public async getAllUsers() {
    try {
      const response = await apiController.get<User[]>('/auth/get-all-user');
      return response;
    } catch (error: any) {
      console.error('Get all users error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to get users',
      };
    }
  }

  // Get single user by ID
  public async getSingleUser(userId: number) {
    try {
      const response = await apiController.get<User>(`/auth/get-single-user?id=${userId}`);
      return response;
    } catch (error: any) {
      console.error('Get single user error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to get user',
      };
    }
  }

  // Change password (through profile update)
  public async changePassword(currentPassword: string, newPassword: string) {
    try {
      const response = await apiController.post('/auth/update-user-profile', {
        oldPassword: currentPassword,
        password: newPassword,
      });
      return response;
    } catch (error: any) {
      console.error('Change password error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to change password',
      };
    }
  }

  // Search users for chat functionality
  public async searchUsers(query: string) {
    try {
      if (!query || query.trim().length < 2) {
        return { success: true, data: [] };
      }

      console.log('🔍 Searching for:', query.trim());
      const response = await apiController.get<{data: User[]}>(`/auth/get-search-users?q=${encodeURIComponent(JSON.stringify(query.trim()))}`);
      console.log('📋 Search response:', response);

      // Extract the nested data structure
      if (response.success && response.data) {
        return {
          success: true,
          data: response.data.data || []
        };
      }

      return {
        success: false,
        error: 'Failed to search users',
        data: []
      };
    } catch (error: any) {
      console.error('Search users error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to search users',
      };
    }
  }

  // Get Stream Chat token
  public async getStreamChatToken() {
    try {
      console.log('🔑 Requesting Stream Chat token...');
      const response = await apiController.get<{
        token: string;
        userId: string;
        appId: string;
        appKey: string;
      }>('/auth/get-stream-chat-token');
      console.log('🔑 Stream Chat token response:', response);
      return response;
    } catch (error: any) {
      console.error('Get Stream Chat token error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to get Stream Chat token',
      };
    }
  }

  // Ensure user exists in Stream Chat
  public async ensureStreamChatUser(userId: string) {
    try {
      console.log('🔄 Ensuring Stream Chat user exists:', userId);
      const response = await apiController.post('/auth/ensure-stream-chat-user', { userId });
      console.log('✅ Stream Chat user ensured:', response);
      return response;
    } catch (error: any) {
      console.error('Ensure Stream Chat user error:', error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Failed to ensure Stream Chat user',
      };
    }
  }

  // Note: The following features are not available in the current backend:
  // - User preferences
  // - Contacts management
  // - Block/unblock users
  // - Online status
  // - Delete account
  // These would need to be implemented in the backend if required
}

// Export singleton instance
export const userServices = new UserServices();
export default userServices;
