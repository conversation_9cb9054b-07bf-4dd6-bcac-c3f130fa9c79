// Email validation
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Password validation
export const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/(?=.*[a-zA-Z])/.test(password)) {
    errors.push('Password must contain at least one letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/(?=.*[@$!%*?&])/.test(password)) {
    errors.push('Password must contain at least one special character (@$!%*?&)');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Phone number validation
export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
};

// Name validation
export const validateName = (name: string): boolean => {
  return name.trim().length >= 2 && /^[a-zA-Z\s]+$/.test(name);
};

// OTP validation
export const validateOtp = (otp: string): boolean => {
  // Backend generates 5-character alphanumeric OTPs, so accept 5-6 alphanumeric characters
  return /^[A-Za-z0-9]{5,6}$/.test(otp);
};

// URL validation
export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

// Username validation
export const validateUsername = (username: string): boolean => {
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
};

// Required field validation
export const validateRequired = (value: string): boolean => {
  return value.trim().length > 0;
};

// Minimum length validation
export const validateMinLength = (value: string, minLength: number): boolean => {
  return value.trim().length >= minLength;
};

// Maximum length validation
export const validateMaxLength = (value: string, maxLength: number): boolean => {
  return value.trim().length <= maxLength;
};

// Confirm password validation
export const validateConfirmPassword = (password: string, confirmPassword: string): boolean => {
  return password === confirmPassword;
};

// Age validation (must be 13 or older)
export const validateAge = (birthDate: Date): boolean => {
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 >= 13;
  }
  
  return age >= 13;
};

// File size validation (in bytes)
export const validateFileSize = (fileSize: number, maxSizeInMB: number): boolean => {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
  return fileSize <= maxSizeInBytes;
};

// File type validation
export const validateFileType = (fileName: string, allowedTypes: string[]): boolean => {
  const fileExtension = fileName.split('.').pop()?.toLowerCase();
  return fileExtension ? allowedTypes.includes(fileExtension) : false;
};

// Image file validation
export const validateImageFile = (fileName: string): boolean => {
  const allowedImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  return validateFileType(fileName, allowedImageTypes);
};

// Video file validation
export const validateVideoFile = (fileName: string): boolean => {
  const allowedVideoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
  return validateFileType(fileName, allowedVideoTypes);
};

// Document file validation
export const validateDocumentFile = (fileName: string): boolean => {
  const allowedDocTypes = ['pdf', 'doc', 'docx', 'txt', 'rtf'];
  return validateFileType(fileName, allowedDocTypes);
};

// Form validation helper
export interface ValidationRule {
  validator: (value: any) => boolean;
  message: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export const validateField = (value: any, rules: ValidationRule[]): ValidationResult => {
  const errors: string[] = [];
  
  for (const rule of rules) {
    if (!rule.validator(value)) {
      errors.push(rule.message);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// Common validation rules
export const validationRules = {
  required: (message: string = 'This field is required'): ValidationRule => ({
    validator: validateRequired,
    message,
  }),
  
  email: (message: string = 'Please enter a valid email address'): ValidationRule => ({
    validator: validateEmail,
    message,
  }),
  
  minLength: (length: number, message?: string): ValidationRule => ({
    validator: (value: string) => validateMinLength(value, length),
    message: message || `Must be at least ${length} characters long`,
  }),
  
  maxLength: (length: number, message?: string): ValidationRule => ({
    validator: (value: string) => validateMaxLength(value, length),
    message: message || `Must be no more than ${length} characters long`,
  }),
  
  phone: (message: string = 'Please enter a valid phone number'): ValidationRule => ({
    validator: validatePhoneNumber,
    message,
  }),
  
  name: (message: string = 'Please enter a valid name'): ValidationRule => ({
    validator: validateName,
    message,
  }),
  
  otp: (message: string = 'Please enter a valid 6-digit OTP'): ValidationRule => ({
    validator: validateOtp,
    message,
  }),
};
