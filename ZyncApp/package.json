{"name": "ZyncApp", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest", "build:android": "cd android && ./gradlew assembleRelease", "build:android-bundle": "cd android && ./gradlew bundleRelease", "build:ios": "cd ios && xcodebuild -workspace ZyncApp.xcworkspace -scheme ZyncApp -configuration Release -destination generic/platform=iOS -archivePath ZyncApp.xcarchive archive", "build:ios-no-signing": "cd ios && xcodebuild -workspace ZyncApp.xcworkspace -scheme ZyncApp -configuration Release -destination 'platform=iOS Simulator,name=iPhone 15' build", "build:ios-simulator": "cd ios && xcodebuild -workspace ZyncApp.xcworkspace -scheme ZyncApp -configuration Release -destination generic/platform=iOS Simulator -archivePath ZyncApp.xcarchive archive", "clean:android": "cd android && ./gradlew clean", "clean:ios": "cd ios && xcodebuild clean -workspace ZyncApp.xcworkspace -scheme ZyncApp"}, "dependencies": {"@bam.tech/react-native-image-resizer": "^3.0.11", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-camera-roll/camera-roll": "^7.10.2", "@react-native-clipboard/clipboard": "^1.16.3", "@react-native-documents/picker": "^10.1.5", "@react-native/new-app-screen": "0.80.2", "@react-navigation/bottom-tabs": "^7.4.5", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.24", "@react-navigation/stack": "^7.4.5", "@stream-io/flat-list-mvcp": "^0.10.3", "axios": "^1.11.0", "expo-crypto": "^14.1.5", "nativewind": "^4.1.23", "react": "19.1.0", "react-native": "0.80.2", "react-native-file-viewer": "^2.1.5", "react-native-gesture-handler": "^2.27.2", "react-native-image-picker": "^8.2.1", "react-native-reanimated": "^3.6.2", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-svg": "^15.12.1", "react-native-vector-icons": "^10.3.0", "stream-chat-react-native": "^8.3.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "19.1.1", "@react-native-community/cli-platform-android": "19.1.1", "@react-native-community/cli-platform-ios": "19.1.1", "@react-native/babel-preset": "0.80.2", "@react-native/eslint-config": "0.80.2", "@react-native/metro-config": "0.80.2", "@react-native/typescript-config": "0.80.2", "@types/jest": "^29.5.13", "@types/react": "^19.1.0", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^19.1.0", "autoprefixer": "^10.4.21", "eslint": "^8.19.0", "jest": "^29.6.3", "postcss": "^8.5.6", "prettier": "2.8.8", "react-test-renderer": "19.1.0", "tailwindcss": "^3.4.17", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}