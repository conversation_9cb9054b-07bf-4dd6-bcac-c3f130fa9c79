
> @web/source@0.0.0 api:serve
> tsx watch --tsconfig ./apps/api/tsconfig.app.json ./apps/api/src/main.ts


----- AuthController -----
Registering MailBuilderService
Registering MailClientService
GET   /auth/get-all
GET   /auth/get-all-user
GET   /auth/get-all-users
GET   /auth/get-by-id
GET   /auth/get-profile
GET   /auth/get-single-user
GET   /auth/get-tfa-status
GET   /auth/get-user-info
POST  /auth/active-account
POST  /auth/create
POST  /auth/create-user
POST  /auth/create-user-by-admin
POST  /auth/forgot-password
POST  /auth/generate-totp-secret
POST  /auth/login
POST  /auth/regenerate-otp
POST  /auth/register
POST  /auth/remove
POST  /auth/remove-totp
POST  /auth/reset-pwd
POST  /auth/send-tfa-email
POST  /auth/set-tfa
POST  /auth/set-thumbnail
POST  /auth/set-totp-secret-tfa
POST  /auth/update
POST  /auth/update-email
POST  /auth/update-profile
POST  /auth/update-pwd
POST  /auth/update-user
POST  /auth/update-user-profile
POST  /auth/user-tfa
POST  /auth/verify-email
POST  /auth/verify-register-otp
POST  /auth/verify-tfa-email
POST  /auth/verify-totp

----- BusStopsController -----
POST  /bus-stops/find-bus-stops-within-radius
POST  /bus-stops/find-by-location

----- CondoController -----
GET   /condo/get-all
GET   /condo/get-by-id
GET   /condo/get-condo-coordinates
GET   /condo/get-latest-transactions-by-location
POST  /condo/find-by-location
POST  /condo/find-condos-near-user-location

----- HawkerController -----
POST  /hawker/find-by-location
POST  /hawker/find-hawker-centers-within-radius

----- HDBController -----
GET   /hdb/get-all
GET   /hdb/get-all-building-types
GET   /hdb/get-all-towns
GET   /hdb/get-buyer-ethnic-group-data
GET   /hdb/get-by-id
GET   /hdb/get-hdb-coordinates
GET   /hdb/get-seller-ethnic-group-data
POST  /hdb/check-ethnic-quota
POST  /hdb/extract-hdb-transaction-data
POST  /hdb/filter-data-by-town
POST  /hdb/find-by-location
POST  /hdb/find-hdb-properties-near-user-location

----- SchoolController -----
GET   /school/get-all
POST  /school/find-schools-within-radius

----- SettingsController -----
GET   /settings/get-all
GET   /settings/get-one
POST  /settings/create
POST  /settings/remove
POST  /settings/update

----- OneMapSearchController -----
GET   /one-map-search/get-search-results

----- HospitalController -----
GET   /hospital/get-hospitals-within-radius

----- MetroController -----
GET   /metro/get-metro-stations-within-radius

----- UserSalesListingController -----
POST  /user-sales-listing/create-sales-listing

----- HDBSalesController -----
GET   /hdb-sales/get-all-sales-listings
GET   /hdb-sales/get-sales-listing-by-id
GET   /hdb-sales/get-sales-listings-by-user-id

----- HDBSalesOfferController -----
GET   /hdb-sales-offer/get-accepted-offer-by-listing-and-seller
GET   /hdb-sales-offer/get-accepted-offers-by-buyer-id
GET   /hdb-sales-offer/get-accepted-offers-by-seller-id
GET   /hdb-sales-offer/get-offer-by-id
GET   /hdb-sales-offer/get-offer-by-listing-id
GET   /hdb-sales-offer/get-offers-by-buyer-id
GET   /hdb-sales-offer/get-offers-by-seller-id
POST  /hdb-sales-offer/accept-offer
POST  /hdb-sales-offer/counter-offer
POST  /hdb-sales-offer/create-offer
POST  /hdb-sales-offer/reject-offer

----- SalesChatController -----
GET   /sales-chat/get-chat-by-id
GET   /sales-chat/get-chat-by-listing-and-users
GET   /sales-chat/get-chats-by-listing-id
GET   /sales-chat/get-chats-by-seller-id
GET   /sales-chat/get-chats-by-user-id
POST  /sales-chat/add-message
POST  /sales-chat/create-chat
POST  /sales-chat/delete-chat
POST  /sales-chat/delete-message
POST  /sales-chat/edit-message
POST  /sales-chat/update-chat-offer

----- HDBSalesTransactionController -----
GET   /hdb-sales-transaction/get-transaction-by-buyer-and-listing
GET   /hdb-sales-transaction/get-transaction-by-seller-and-listing
GET   /hdb-sales-transaction/get-transactions-by-buyer
GET   /hdb-sales-transaction/get-transactions-by-seller
POST  /hdb-sales-transaction/create-transaction
POST  /hdb-sales-transaction/update-checkpoint
Server is running: http://:::3333
